a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:55036:"a:1:{s:8:"messages";a:54:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.334711;i:4;a:0:{}i:5;i:2611944;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.338704;i:4;a:0:{}i:5;i:2728952;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.340227;i:4;a:0:{}i:5;i:2770160;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.34025;i:4;a:0:{}i:5;i:2770536;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.377895;i:4;a:0:{}i:5;i:3915736;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.40122;i:4;a:0:{}i:5;i:4725992;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.412042;i:4;a:0:{}i:5;i:5115656;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.422354;i:4;a:0:{}i:5;i:5580064;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.423591;i:4;a:0:{}i:5;i:5607456;}i:20;a:6:{i:0;s:55:"Route requested: 'api/raw-material/update-raw-material'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.427499;i:4;a:0:{}i:5;i:5781112;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.427517;i:4;a:0:{}i:5;i:5782760;}i:22;a:6:{i:0;s:50:"Route to run: api/raw-material/update-raw-material";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.439358;i:4;a:0:{}i:5;i:6200072;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.472151;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7868496;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502872;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8072216;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516022;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8118128;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.522123;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8413936;}i:35;a:6:{i:0;s:55:"User '6' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.525806;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8700584;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.525899;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8701176;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.531533;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8888760;}i:40;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53317;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8895384;}i:43;a:6:{i:0;s:25:"Checking role: raw_keeper";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.534363;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8898776;}i:44;a:6:{i:0;s:92:"Running action: app\modules\api\controllers\RawMaterialController::actionUpdateRawMaterial()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.534378;i:4;a:0:{}i:5;i:8897936;}i:45;a:6:{i:0;s:94:"Failed to set unsafe attribute 'invoice_id' in 'app\modules\api\models\IncomeRawMaterialForm'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.535606;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:374;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:9103832;}i:46;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.537625;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:94;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9168152;}i:47;a:6:{i:0;s:125:"SELECT * FROM "invoice" WHERE ("id"=42) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL) AND ("accept_user_id" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.540737;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9216672;}i:50;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.542343;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9228728;}i:53;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.5457;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9240088;}i:56;a:6:{i:0;s:81:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"=42) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.54977;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9270768;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551575;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9283064;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554488;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293976;}i:65;a:6:{i:0;s:86:"SELECT * FROM "material_storage_history" WHERE ("invoice_detail_id"=46) AND ("type"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.556778;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9336632;}i:68;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.55804;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349696;}i:71;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.560918;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9361752;}i:74;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.563085;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9397112;}i:77;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.566219;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9407472;}i:80;a:6:{i:0;s:46:"SELECT * FROM "material_storage" WHERE "id"=30";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.568048;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9414880;}i:83;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=11)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.570387;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9518840;}i:86;a:6:{i:0;s:94:"UPDATE "material_storage" SET "quantity"='0', "updated_at"='2025-06-01 17:22:28' WHERE "id"=30";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.571771;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9519152;}i:89;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572904;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:123;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9580632;}i:92;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=42)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573849;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9591384;}i:95;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574106;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9596792;}i:98;a:6:{i:0;s:222:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (42, 13, NULL, 7, 7, '2025-06-01 17:22:28', NULL, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574337;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9600504;}i:101;a:6:{i:0;s:145:"SELECT * FROM "material_storage" WHERE ("material_id"=13) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.57521;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:230;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9614304;}i:104;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575811;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9631160;}i:107;a:6:{i:0;s:162:"INSERT INTO "material_storage" ("material_id", "created_at", "quantity", "updated_at") VALUES (13, '2025-06-01 17:22:28', 7, '2025-06-01 17:22:28') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.576734;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9667400;}i:110;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.577994;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9689184;}i:113;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=31)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.57853;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9695712;}i:116;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=47)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.579247;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9702272;}i:119;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (31, 13, 7, '2025-06-01 17:22:28', 6, 1, 47) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580301;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9706712;}i:122;a:6:{i:0;s:42:"DELETE FROM "invoice_detail" WHERE "id"=46";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.581177;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:161;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9696664;}i:125;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581743;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9712696;}i:128;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584811;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9723488;}i:131;a:6:{i:0;s:347:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'update_raw_material', 'invoice', '"42"'::jsonb, '"{\"invoice_number\":\"20250601-66088C4E\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"updated_materials\":[{\"material_id\":13,\"quantity\":7}]}"'::jsonb, '2025-06-01 17:22:28')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.586932;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9744784;}i:134;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:**********.588048;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:168;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9743312;}}}";s:9:"profiling";s:100291:"a:3:{s:6:"memory";i:9935448;s:4:"time";d:0.28923916816711426;s:8:"messages";a:70:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.472179;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7870000;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.501974;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7872304;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502904;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074168;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514936;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8089904;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516044;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8119992;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.51813;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8121928;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.522188;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418200;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.523685;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8420960;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.531581;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891368;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53293;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893536;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.533182;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8897992;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.534132;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900096;}i:48;a:6:{i:0;s:125:"SELECT * FROM "invoice" WHERE ("id"=42) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL) AND ("accept_user_id" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.540798;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9219736;}i:49;a:6:{i:0;s:125:"SELECT * FROM "invoice" WHERE ("id"=42) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL) AND ("accept_user_id" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.542134;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222880;}i:51;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.54237;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9230592;}i:52;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.545229;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9249600;}i:54;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.545727;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9241952;}i:55;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548274;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9245344;}i:57;a:6:{i:0;s:81:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"=42) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.549797;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273784;}i:58;a:6:{i:0;s:81:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"=42) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551345;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277096;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551599;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9284928;}i:61;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554185;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9302464;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554506;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9295840;}i:64;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.556199;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9299816;}i:66;a:6:{i:0;s:86:"SELECT * FROM "material_storage_history" WHERE ("invoice_detail_id"=46) AND ("type"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.556793;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9340064;}i:67;a:6:{i:0;s:86:"SELECT * FROM "material_storage_history" WHERE ("invoice_detail_id"=46) AND ("type"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.557836;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9343832;}i:69;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.558061;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9351936;}i:70;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.560629;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9369968;}i:72;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.560934;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9363992;}i:73;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.562578;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9369008;}i:75;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.563105;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9399352;}i:76;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.56574;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9412464;}i:78;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.566239;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9409712;}i:79;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.567805;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9412920;}i:81;a:6:{i:0;s:46:"SELECT * FROM "material_storage" WHERE "id"=30";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.568063;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9417600;}i:82;a:6:{i:0;s:46:"SELECT * FROM "material_storage" WHERE "id"=30";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.569211;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9420472;}i:84;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=11)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.570405;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9522216;}i:85;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=11)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.571514;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9524232;}i:87;a:6:{i:0;s:94:"UPDATE "material_storage" SET "quantity"='0', "updated_at"='2025-06-01 17:22:28' WHERE "id"=30";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.571785;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9521952;}i:88;a:6:{i:0;s:94:"UPDATE "material_storage" SET "quantity"='0', "updated_at"='2025-06-01 17:22:28' WHERE "id"=30";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.572308;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9524000;}i:90;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572919;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:123;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9583632;}i:91;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573586;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:123;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9585240;}i:93;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=42)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573864;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9594384;}i:94;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=42)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574005;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9596024;}i:96;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574117;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9599792;}i:97;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.57423;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9601432;}i:99;a:6:{i:0;s:222:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (42, 13, NULL, 7, 7, '2025-06-01 17:22:28', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574342;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9600992;}i:100;a:6:{i:0;s:222:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (42, 13, NULL, 7, 7, '2025-06-01 17:22:28', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574816;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9603320;}i:102;a:6:{i:0;s:145:"SELECT * FROM "material_storage" WHERE ("material_id"=13) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575222;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:230;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9617792;}i:103;a:6:{i:0;s:145:"SELECT * FROM "material_storage" WHERE ("material_id"=13) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575611;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:230;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9620240;}i:105;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575828;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9634536;}i:106;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.576262;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9636552;}i:108;a:6:{i:0;s:162:"INSERT INTO "material_storage" ("material_id", "created_at", "quantity", "updated_at") VALUES (13, '2025-06-01 17:22:28', 7, '2025-06-01 17:22:28') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.576741;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9668904;}i:109;a:6:{i:0;s:162:"INSERT INTO "material_storage" ("material_id", "created_at", "quantity", "updated_at") VALUES (13, '2025-06-01 17:22:28', 7, '2025-06-01 17:22:28') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.577444;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9671416;}i:111;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578015;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9692560;}i:112;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578345;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9694576;}i:114;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=31)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578548;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9699104;}i:115;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=31)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578802;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9701120;}i:117;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=47)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.579277;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9705664;}i:118;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=47)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.579827;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9707680;}i:120;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (31, 13, 7, '2025-06-01 17:22:28', 6, 1, 47) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580316;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9708216;}i:121;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (31, 13, 7, '2025-06-01 17:22:28', 6, 1, 47) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580998;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9710912;}i:123;a:6:{i:0;s:42:"DELETE FROM "invoice_detail" WHERE "id"=46";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.581193;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:161;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9699648;}i:124;a:6:{i:0;s:42:"DELETE FROM "invoice_detail" WHERE "id"=46";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.581387;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:161;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9701240;}i:126;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581764;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9714936;}i:127;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584591;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9729416;}i:129;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584823;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9729824;}i:130;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58642;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9733008;}i:132;a:6:{i:0;s:347:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'update_raw_material', 'invoice', '"42"'::jsonb, '"{\"invoice_number\":\"20250601-66088C4E\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"updated_materials\":[{\"material_id\":13,\"quantity\":7}]}"'::jsonb, '2025-06-01 17:22:28')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.586946;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9748408;}i:133;a:6:{i:0;s:347:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'update_raw_material', 'invoice', '"42"'::jsonb, '"{\"invoice_number\":\"20250601-66088C4E\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"updated_materials\":[{\"material_id\":13,\"quantity\":7}]}"'::jsonb, '2025-06-01 17:22:28')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.587982;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9750544;}}}";s:2:"db";s:99043:"a:1:{s:8:"messages";a:68:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502904;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074168;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514936;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8089904;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516044;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8119992;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.51813;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8121928;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.522188;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418200;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.523685;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8420960;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.531581;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891368;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53293;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893536;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.533182;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8897992;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.534132;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900096;}i:48;a:6:{i:0;s:125:"SELECT * FROM "invoice" WHERE ("id"=42) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL) AND ("accept_user_id" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.540798;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9219736;}i:49;a:6:{i:0;s:125:"SELECT * FROM "invoice" WHERE ("id"=42) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL) AND ("accept_user_id" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.542134;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222880;}i:51;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.54237;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9230592;}i:52;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.545229;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9249600;}i:54;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.545727;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9241952;}i:55;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.548274;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:103;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9245344;}i:57;a:6:{i:0;s:81:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"=42) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.549797;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273784;}i:58;a:6:{i:0;s:81:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"=42) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551345;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277096;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551599;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9284928;}i:61;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554185;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9302464;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554506;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9295840;}i:64;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.556199;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9299816;}i:66;a:6:{i:0;s:86:"SELECT * FROM "material_storage_history" WHERE ("invoice_detail_id"=46) AND ("type"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.556793;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9340064;}i:67;a:6:{i:0;s:86:"SELECT * FROM "material_storage_history" WHERE ("invoice_detail_id"=46) AND ("type"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.557836;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9343832;}i:69;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.558061;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9351936;}i:70;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.560629;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9369968;}i:72;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.560934;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9363992;}i:73;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.562578;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:198;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9369008;}i:75;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.563105;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9399352;}i:76;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.56574;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9412464;}i:78;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.566239;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9409712;}i:79;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.567805;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9412920;}i:81;a:6:{i:0;s:46:"SELECT * FROM "material_storage" WHERE "id"=30";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.568063;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9417600;}i:82;a:6:{i:0;s:46:"SELECT * FROM "material_storage" WHERE "id"=30";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.569211;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:202;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9420472;}i:84;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=11)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.570405;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9522216;}i:85;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=11)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.571514;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9524232;}i:87;a:6:{i:0;s:94:"UPDATE "material_storage" SET "quantity"='0', "updated_at"='2025-06-01 17:22:28' WHERE "id"=30";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.571785;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9521952;}i:88;a:6:{i:0;s:94:"UPDATE "material_storage" SET "quantity"='0', "updated_at"='2025-06-01 17:22:28' WHERE "id"=30";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.572308;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:207;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:117;s:8:"function";s:23:"revertOldStorageChanges";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9524000;}i:90;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572919;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:123;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9583632;}i:91;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573586;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:123;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9585240;}i:93;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=42)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573864;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9594384;}i:94;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=42)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574005;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9596024;}i:96;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574117;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9599792;}i:97;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.57423;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9601432;}i:99;a:6:{i:0;s:222:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (42, 13, NULL, 7, 7, '2025-06-01 17:22:28', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574342;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9600992;}i:100;a:6:{i:0;s:222:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (42, 13, NULL, 7, 7, '2025-06-01 17:22:28', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574816;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:150;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9603320;}i:102;a:6:{i:0;s:145:"SELECT * FROM "material_storage" WHERE ("material_id"=13) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575222;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:230;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9617792;}i:103;a:6:{i:0;s:145:"SELECT * FROM "material_storage" WHERE ("material_id"=13) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575611;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:230;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9620240;}i:105;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.575828;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9634536;}i:106;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.576262;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9636552;}i:108;a:6:{i:0;s:162:"INSERT INTO "material_storage" ("material_id", "created_at", "quantity", "updated_at") VALUES (13, '2025-06-01 17:22:28', 7, '2025-06-01 17:22:28') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.576741;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9668904;}i:109;a:6:{i:0;s:162:"INSERT INTO "material_storage" ("material_id", "created_at", "quantity", "updated_at") VALUES (13, '2025-06-01 17:22:28', 7, '2025-06-01 17:22:28') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.577444;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:242;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9671416;}i:111;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578015;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9692560;}i:112;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=13)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578345;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9694576;}i:114;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=31)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578548;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9699104;}i:115;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=31)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.578802;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9701120;}i:117;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=47)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.579277;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9705664;}i:118;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=47)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.579827;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9707680;}i:120;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (31, 13, 7, '2025-06-01 17:22:28', 6, 1, 47) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580316;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9708216;}i:121;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (31, 13, 7, '2025-06-01 17:22:28', 6, 1, 47) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580998;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:256;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:155;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9710912;}i:123;a:6:{i:0;s:42:"DELETE FROM "invoice_detail" WHERE "id"=46";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.581193;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:161;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9699648;}i:124;a:6:{i:0;s:42:"DELETE FROM "invoice_detail" WHERE "id"=46";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.581387;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:161;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:378;s:8:"function";s:20:"updateMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9701240;}i:126;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.581764;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9714936;}i:127;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584591;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9729416;}i:129;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584823;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9729824;}i:130;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58642;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9733008;}i:132;a:6:{i:0;s:347:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'update_raw_material', 'invoice', '"42"'::jsonb, '"{\"invoice_number\":\"20250601-66088C4E\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"updated_materials\":[{\"material_id\":13,\"quantity\":7}]}"'::jsonb, '2025-06-01 17:22:28')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.586946;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9748408;}i:133;a:6:{i:0;s:347:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'update_raw_material', 'invoice', '"42"'::jsonb, '"{\"invoice_number\":\"20250601-66088C4E\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"updated_materials\":[{\"material_id\":13,\"quantity\":7}]}"'::jsonb, '2025-06-01 17:22:28')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.587982;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:282;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeUpdateService.php";s:4:"line";i:166;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeUpdateService";s:4:"type";s:2:"->";}}i:5;i:9750544;}}}";s:5:"event";s:15016:"a:82:{i:0;a:5:{s:4:"time";d:**********.426339;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.439654;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.439665;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.463192;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.501964;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.523781;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.52381;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.523949;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.525468;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.52548;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.525486;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.52549;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.525493;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.525631;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.525671;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.525853;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.525974;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:17;a:5:{s:4:"time";d:**********.535679;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:18;a:5:{s:4:"time";d:**********.535729;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:19;a:5:{s:4:"time";d:**********.537684;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:20;a:5:{s:4:"time";d:**********.539088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:**********.542291;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:22;a:5:{s:4:"time";d:**********.548958;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:23;a:5:{s:4:"time";d:**********.549668;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:**********.551518;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:25;a:5:{s:4:"time";d:**********.556412;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:26;a:5:{s:4:"time";d:**********.55671;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:**********.558002;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:28;a:5:{s:4:"time";d:**********.562786;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:29;a:5:{s:4:"time";d:**********.563054;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:30;a:5:{s:4:"time";d:**********.569355;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:31;a:5:{s:4:"time";d:**********.569379;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:32;a:5:{s:4:"time";d:**********.569407;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:33;a:5:{s:4:"time";d:**********.570303;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:34;a:5:{s:4:"time";d:**********.570326;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:**********.571689;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:36;a:5:{s:4:"time";d:**********.5717;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:37;a:5:{s:4:"time";d:**********.572507;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:38;a:5:{s:4:"time";d:**********.572522;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:39;a:5:{s:4:"time";d:**********.572838;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:**********.572857;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:**********.573656;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:42;a:5:{s:4:"time";d:**********.573664;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:43;a:5:{s:4:"time";d:**********.573676;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:44;a:5:{s:4:"time";d:**********.573681;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:45;a:5:{s:4:"time";d:**********.573698;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:46;a:5:{s:4:"time";d:**********.573795;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:**********.573809;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:**********.574058;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:**********.574072;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:**********.574273;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:51;a:5:{s:4:"time";d:**********.57428;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:52;a:5:{s:4:"time";d:**********.574873;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:53;a:5:{s:4:"time";d:**********.574883;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:54;a:5:{s:4:"time";d:**********.575659;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:55;a:5:{s:4:"time";d:**********.575678;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:56;a:5:{s:4:"time";d:**********.575757;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:**********.575771;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:58;a:5:{s:4:"time";d:**********.576343;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:59;a:5:{s:4:"time";d:**********.57635;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:60;a:5:{s:4:"time";d:**********.577679;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:61;a:5:{s:4:"time";d:**********.577691;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:62;a:5:{s:4:"time";d:**********.577733;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:63;a:5:{s:4:"time";d:**********.577908;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:**********.577934;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:65;a:5:{s:4:"time";d:**********.57845;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:66;a:5:{s:4:"time";d:**********.578474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:67;a:5:{s:4:"time";d:**********.579035;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:68;a:5:{s:4:"time";d:**********.579104;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:69;a:5:{s:4:"time";d:**********.580098;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:70;a:5:{s:4:"time";d:**********.580124;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:71;a:5:{s:4:"time";d:**********.581105;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:72;a:5:{s:4:"time";d:**********.581122;s:4:"name";s:12:"beforeDelete";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:73;a:5:{s:4:"time";d:**********.581432;s:4:"name";s:11:"afterDelete";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:74;a:5:{s:4:"time";d:**********.59032;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:75;a:5:{s:4:"time";d:**********.590618;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:76;a:5:{s:4:"time";d:**********.590958;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:77;a:5:{s:4:"time";d:**********.590963;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:78;a:5:{s:4:"time";d:**********.590969;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:79;a:5:{s:4:"time";d:**********.590973;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:80;a:5:{s:4:"time";d:**********.591726;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:81;a:5:{s:4:"time";d:**********.591771;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:**********.307258;s:3:"end";d:**********.598212;s:6:"memory";i:10012360;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2240:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427296;i:4;a:0:{}i:5;i:5773216;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427332;i:4;a:0:{}i:5;i:5773968;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.42735;i:4;a:0:{}i:5;i:5774720;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427365;i:4;a:0:{}i:5;i:5775472;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.42738;i:4;a:0:{}i:5;i:5776224;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427393;i:4;a:0:{}i:5;i:5776976;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427405;i:4;a:0:{}i:5;i:5777728;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427425;i:4;a:0:{}i:5;i:5778480;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427438;i:4;a:0:{}i:5;i:5779872;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.427472;i:4;a:0:{}i:5;i:5781968;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.427485;i:4;a:0:{}i:5;i:5781784;}}s:5:"route";s:36:"api/raw-material/update-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionUpdateRawMaterial()";}";s:7:"request";s:4721:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"b798fb93-5eeb-4bef-aaa0-4a5e6687ae4a";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"221";s:6:"cookie";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=fb7ef1968eaf02bc109a72ab8ea23e79c838f4e6c346a4efaccdc3fe4875dceca%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22ayYC-tROroZqNZ4fCWX6HWCPNuIEbHuT%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683c460464d43";s:16:"X-Debug-Duration";s:3:"285";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683c460464d43";s:10:"Set-Cookie";s:204:"_csrf=de2869ada5cc56c530703a592781ec4194d4e0d6e0c7b0462a696cd147286f24a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%229VXIpreJOs01MC_RfEijaZ4ZDDuIpQNI%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:36:"api/raw-material/update-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionUpdateRawMaterial()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:221:"{
    "invoice_id": 42,
    "supplier_id": 1,
    "description": "Поставка материалов",
    "materials": [
        {
            "material_id": 13,
            "quantity": 7
        }
    ]
}
";s:7:"Decoded";a:4:{s:10:"invoice_id";i:42;s:11:"supplier_id";i:1;s:11:"description";s:37:"Поставка материалов";s:9:"materials";a:1:{i:0;a:2:{s:11:"material_id";i:13;s:8:"quantity";i:7;}}}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"b798fb93-5eeb-4bef-aaa0-4a5e6687ae4a";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"221";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=fb7ef1968eaf02bc109a72ab8ea23e79c838f4e6c346a4efaccdc3fe4875dceca%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22ayYC-tROroZqNZ4fCWX6HWCPNuIEbHuT%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"56220";s:12:"REDIRECT_URL";s:37:"/api/raw-material/update-raw-material";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:37:"/api/raw-material/update-raw-material";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.273247;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"ijocj4ons9v73rb97jao7352v8c6b4cn";s:5:"_csrf";s:130:"fb7ef1968eaf02bc109a72ab8ea23e79c838f4e6c346a4efaccdc3fe4875dceca:2:{i:0;s:5:"_csrf";i:1;s:32:"ayYC-tROroZqNZ4fCWX6HWCPNuIEbHuT";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2139:"a:5:{s:2:"id";i:6;s:8:"identity";a:8:{s:2:"id";s:1:"6";s:8:"username";s:12:"'raw_keeper'";s:9:"full_name";s:20:"'Hom ashyo ishchisi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";s:8:"password";s:62:"'$2y$13$bDj/q/7SKFwJIrGKBX7Z8O5TvDtnhjH0Q9YZKxl858G.7vVP8/a.m'";s:10:"created_at";s:21:"'2025-03-16 16:22:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:10:"raw_keeper";a:7:{s:4:"type";i:1;s:4:"name";s:10:"raw_keeper";s:11:"description";s:10:"Raw keeper";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683c460464d43";s:3:"url";s:50:"http://silver/api/raw-material/update-raw-material";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.273247;s:10:"statusCode";i:200;s:8:"sqlCount";i:34;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9935448;s:14:"processingTime";d:0.28923916816711426;}s:10:"exceptions";a:0:{}}