a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:57992:"a:1:{s:8:"messages";a:50:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.5915;i:4;a:0:{}i:5;i:2611912;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.596365;i:4;a:0:{}i:5;i:2728920;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.597589;i:4;a:0:{}i:5;i:2770128;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.597604;i:4;a:0:{}i:5;i:2770504;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.640126;i:4;a:0:{}i:5;i:3915704;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.669408;i:4;a:0:{}i:5;i:4725960;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.682489;i:4;a:0:{}i:5;i:5115624;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.695118;i:4;a:0:{}i:5;i:5580032;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.696795;i:4;a:0:{}i:5;i:5607424;}i:20;a:6:{i:0;s:55:"Route requested: 'api/raw-material/accept-raw-material'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.70361;i:4;a:0:{}i:5;i:5781080;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.703632;i:4;a:0:{}i:5;i:5782728;}i:22;a:6:{i:0;s:50:"Route to run: api/raw-material/accept-raw-material";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.716322;i:4;a:0:{}i:5;i:6237440;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.748797;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7905760;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.815002;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8109480;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864925;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8155392;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877371;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8451200;}i:35;a:6:{i:0;s:55:"User '6' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.888388;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8737848;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.888457;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8738440;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892062;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8925984;}i:40;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897518;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8932608;}i:43;a:6:{i:0;s:25:"Checking role: raw_keeper";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.902748;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8936000;}i:44;a:6:{i:0;s:92:"Running action: app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.902806;i:4;a:0:{}i:5;i:8935160;}i:45;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.90905;i:4;a:2:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:96;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9202696;}i:46;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910065;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9246360;}i:49;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.916533;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9258848;}i:52;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.923397;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9378488;}i:55;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-EE82E43D', 1, 'Поставка материалов', 2, '2025-06-01 16:51:33', 0, NULL, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.927178;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9415872;}i:58;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.933194;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9448464;}i:61;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.93994;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9460504;}i:64;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=36)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.944168;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9481448;}i:67;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.946676;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9528224;}i:70;a:6:{i:0;s:223:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (36, 4, NULL, 10, 10, '2025-06-01 16:51:33', NULL, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.949512;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9533480;}i:73;a:6:{i:0;s:144:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.953918;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9580808;}i:76;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956468;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9592648;}i:79;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.962014;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9603008;}i:82;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.965686;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9619080;}i:85;a:6:{i:0;s:95:"UPDATE "material_storage" SET "quantity"='20', "updated_at"='2025-06-01 16:51:33' WHERE "id"=28";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.966784;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9618664;}i:88;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968825;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9657952;}i:91;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.975404;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9670008;}i:94;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979827;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9691960;}i:97;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=28)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981167;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9699128;}i:100;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=44)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982571;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9705688;}i:103;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (28, 4, 10, '2025-06-01 16:51:33', 6, 1, 44) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984873;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9710184;}i:106;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.988299;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9727704;}i:109;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.994488;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9738504;}i:112;a:6:{i:0;s:155:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status", "accepted_at") VALUES (8, 36, '2025-06-01 16:51:33', 0, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.998364;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:259;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9753144;}i:115;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.001704;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9760448;}i:118;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.00754;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9771240;}i:121;a:6:{i:0;s:363:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'accept_raw_material', 'invoice', NULL, '"{\"invoice_id\":36,\"invoice_number\":\"20250601-EE82E43D\",\"supplier_id\":1,\"total_amount\":0,\"materials_count\":1,\"materials\":[{\"material_id\":4,\"quantity\":10}]}"'::jsonb, '2025-06-01 16:51:34')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748778694.012044;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9854040;}i:124;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1748778694.015352;i:4;a:2:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:114;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9852824;}}}";s:9:"profiling";s:107087:"a:3:{s:6:"memory";i:9987696;s:4:"time";d:0.***************;s:8:"messages";a:64:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.748831;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7907264;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.813011;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7909568;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.81508;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8111432;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862464;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8127168;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.865017;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8157256;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.870561;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8159192;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877421;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8455464;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.882788;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8458224;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892106;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8928592;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.896496;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8930760;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897559;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8935216;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901728;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8937320;}i:47;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910113;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9248600;}i:48;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.915859;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9267984;}i:50;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.916564;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9261088;}i:51;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.919668;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9264856;}i:53;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.923434;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9381864;}i:54;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.926104;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9383848;}i:56;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-EE82E43D', 1, 'Поставка материалов', 2, '2025-06-01 16:51:33', 0, NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.927193;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9417376;}i:57;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-EE82E43D', 1, 'Поставка материалов', 2, '2025-06-01 16:51:33', 0, NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.931393;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9420016;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.933259;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9450704;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.939242;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9468616;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.939975;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9462744;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943331;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9467096;}i:65;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=36)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.944199;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9490456;}i:66;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=36)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.945785;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9492472;}i:68;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.946703;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9531600;}i:69;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.949043;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9533584;}i:71;a:6:{i:0;s:223:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (36, 4, NULL, 10, 10, '2025-06-01 16:51:33', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.949526;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9534984;}i:72;a:6:{i:0;s:223:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (36, 4, NULL, 10, 10, '2025-06-01 16:51:33', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.951903;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9537720;}i:74;a:6:{i:0;s:144:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.953986;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9584296;}i:75;a:6:{i:0;s:144:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956195;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9587216;}i:77;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956506;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9594888;}i:78;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.961313;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9608000;}i:80;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.962043;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9605248;}i:81;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.964919;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9608456;}i:83;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.965712;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9622456;}i:84;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.966376;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9624440;}i:86;a:6:{i:0;s:95:"UPDATE "material_storage" SET "quantity"='20', "updated_at"='2025-06-01 16:51:33' WHERE "id"=28";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.966807;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9621464;}i:87;a:6:{i:0;s:95:"UPDATE "material_storage" SET "quantity"='20', "updated_at"='2025-06-01 16:51:33' WHERE "id"=28";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.968001;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9623512;}i:89;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968872;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9660192;}i:90;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974783;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9678224;}i:92;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.975438;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9672248;}i:93;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.97888;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9677264;}i:95;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979856;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9695336;}i:96;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.980635;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9697320;}i:98;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=28)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981194;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9702520;}i:99;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=28)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981933;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9704536;}i:101;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=44)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982598;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9709080;}i:102;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=44)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984399;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9711096;}i:104;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (28, 4, 10, '2025-06-01 16:51:33', 6, 1, 44) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984889;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9711688;}i:105;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (28, 4, 10, '2025-06-01 16:51:33', 6, 1, 44) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.987111;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9714384;}i:107;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.988367;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9729944;}i:108;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.993801;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9744496;}i:110;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.994519;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9740744;}i:111;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.997607;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9743056;}i:113;a:6:{i:0;s:155:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status", "accepted_at") VALUES (8, 36, '2025-06-01 16:51:33', 0, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.998379;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:259;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9754648;}i:114;a:6:{i:0;s:155:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status", "accepted_at") VALUES (8, 36, '2025-06-01 16:51:33', 0, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.000703;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:259;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9757200;}i:116;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.00174;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9762688;}i:117;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.006853;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9777168;}i:119;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.007572;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9773480;}i:120;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.010775;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9776664;}i:122;a:6:{i:0;s:363:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'accept_raw_material', 'invoice', NULL, '"{\"invoice_id\":36,\"invoice_number\":\"20250601-EE82E43D\",\"supplier_id\":1,\"total_amount\":0,\"materials_count\":1,\"materials\":[{\"material_id\":4,\"quantity\":10}]}"'::jsonb, '2025-06-01 16:51:34')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748778694.012072;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9857664;}i:123;a:6:{i:0;s:363:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'accept_raw_material', 'invoice', NULL, '"{\"invoice_id\":36,\"invoice_number\":\"20250601-EE82E43D\",\"supplier_id\":1,\"total_amount\":0,\"materials_count\":1,\"materials\":[{\"material_id\":4,\"quantity\":10}]}"'::jsonb, '2025-06-01 16:51:34')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748778694.015082;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9859800;}}}";s:2:"db";s:105841:"a:1:{s:8:"messages";a:62:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.81508;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8111432;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862464;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8127168;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.865017;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8157256;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.870561;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8159192;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877421;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8455464;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.882788;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:36;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8458224;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892106;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8928592;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.896496;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8930760;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.897559;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8935216;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901728;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:41;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8937320;}i:47;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910113;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9248600;}i:48;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.915859;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9267984;}i:50;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.916564;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9261088;}i:51;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.919668;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9264856;}i:53;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.923434;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9381864;}i:54;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.926104;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9383848;}i:56;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-EE82E43D', 1, 'Поставка материалов', 2, '2025-06-01 16:51:33', 0, NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.927193;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9417376;}i:57;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-EE82E43D', 1, 'Поставка материалов', 2, '2025-06-01 16:51:33', 0, NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.931393;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9420016;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.933259;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9450704;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.939242;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9468616;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.939975;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9462744;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943331;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9467096;}i:65;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=36)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.944199;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9490456;}i:66;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=36)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.945785;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9492472;}i:68;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.946703;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9531600;}i:69;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.949043;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9533584;}i:71;a:6:{i:0;s:223:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (36, 4, NULL, 10, 10, '2025-06-01 16:51:33', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.949526;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9534984;}i:72;a:6:{i:0;s:223:"INSERT INTO "invoice_detail" ("invoice_id", "material_id", "price", "quantity", "remainder_quantity", "created_at", "deleted_at", "currency_id") VALUES (36, 4, NULL, 10, 10, '2025-06-01 16:51:33', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.951903;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9537720;}i:74;a:6:{i:0;s:144:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.953986;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9584296;}i:75;a:6:{i:0;s:144:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("updated_at" >= '2025-06-01 00:00:00') AND ("updated_at" <= '2025-06-01 23:59:59')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956195;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9587216;}i:77;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956506;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9594888;}i:78;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.961313;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9608000;}i:80;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.962043;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9605248;}i:81;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.964919;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:200;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9608456;}i:83;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.965712;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9622456;}i:84;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.966376;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9624440;}i:86;a:6:{i:0;s:95:"UPDATE "material_storage" SET "quantity"='20', "updated_at"='2025-06-01 16:51:33' WHERE "id"=28";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.966807;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9621464;}i:87;a:6:{i:0;s:95:"UPDATE "material_storage" SET "quantity"='20', "updated_at"='2025-06-01 16:51:33' WHERE "id"=28";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.968001;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:212;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9623512;}i:89;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968872;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9660192;}i:90;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974783;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9678224;}i:92;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.975438;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9672248;}i:93;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.97888;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:231;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9677264;}i:95;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979856;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9695336;}i:96;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.980635;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9697320;}i:98;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=28)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981194;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9702520;}i:99;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=28)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981933;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9704536;}i:101;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=44)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.982598;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9709080;}i:102;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=44)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984399;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9711096;}i:104;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (28, 4, 10, '2025-06-01 16:51:33', 6, 1, 44) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984889;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9711688;}i:105;a:6:{i:0;s:214:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (28, 4, 10, '2025-06-01 16:51:33', 6, 1, 44) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.987111;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:239;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:217;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:105;s:8:"function";s:21:"updateMaterialStorage";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9714384;}i:107;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.988367;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9729944;}i:108;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.993801;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9744496;}i:110;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.994519;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9740744;}i:111;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.997607;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:253;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9743056;}i:113;a:6:{i:0;s:155:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status", "accepted_at") VALUES (8, 36, '2025-06-01 16:51:33', 0, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.998379;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:259;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9754648;}i:114;a:6:{i:0;s:155:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status", "accepted_at") VALUES (8, 36, '2025-06-01 16:51:33', 0, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.000703;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:259;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:109;s:8:"function";s:14:"createTracking";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:310;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9757200;}i:116;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.00174;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9762688;}i:117;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.006853;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9777168;}i:119;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.007572;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9773480;}i:120;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748778694.010775;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9776664;}i:122;a:6:{i:0;s:363:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'accept_raw_material', 'invoice', NULL, '"{\"invoice_id\":36,\"invoice_number\":\"20250601-EE82E43D\",\"supplier_id\":1,\"total_amount\":0,\"materials_count\":1,\"materials\":[{\"material_id\":4,\"quantity\":10}]}"'::jsonb, '2025-06-01 16:51:34')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748778694.012072;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9857664;}i:123;a:6:{i:0;s:363:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'accept_raw_material', 'invoice', NULL, '"{\"invoice_id\":36,\"invoice_number\":\"20250601-EE82E43D\",\"supplier_id\":1,\"total_amount\":0,\"materials_count\":1,\"materials\":[{\"material_id\":4,\"quantity\":10}]}"'::jsonb, '2025-06-01 16:51:34')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748778694.015082;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:287;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:112;s:8:"function";s:9:"logAction";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9859800;}}}";s:5:"event";s:12690:"a:69:{i:0;a:5:{s:4:"time";d:**********.702234;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.716616;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.716636;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.741766;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.812989;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.883442;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.883512;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.883881;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.888186;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.88822;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.888235;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.888246;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.888255;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.888264;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.888274;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.888426;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.888504;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:17;a:5:{s:4:"time";d:**********.907246;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:18;a:5:{s:4:"time";d:**********.907472;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:19;a:5:{s:4:"time";d:**********.909089;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:20;a:5:{s:4:"time";d:**********.909955;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:21;a:5:{s:4:"time";d:**********.920174;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:22;a:5:{s:4:"time";d:**********.922506;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:**********.922626;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:**********.926383;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:25;a:5:{s:4:"time";d:**********.926402;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:26;a:5:{s:4:"time";d:**********.932393;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:27;a:5:{s:4:"time";d:**********.933062;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:28;a:5:{s:4:"time";d:**********.94377;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:29;a:5:{s:4:"time";d:**********.943994;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:30;a:5:{s:4:"time";d:**********.944076;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:31;a:5:{s:4:"time";d:**********.946514;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:**********.946586;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:33;a:5:{s:4:"time";d:**********.949357;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:34;a:5:{s:4:"time";d:**********.949376;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:35;a:5:{s:4:"time";d:**********.952193;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:36;a:5:{s:4:"time";d:**********.952996;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:**********.956393;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:38;a:5:{s:4:"time";d:**********.965353;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:39;a:5:{s:4:"time";d:**********.965394;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:40;a:5:{s:4:"time";d:**********.965572;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:**********.965603;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:**********.966656;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:43;a:5:{s:4:"time";d:**********.966672;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:44;a:5:{s:4:"time";d:**********.968215;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:45;a:5:{s:4:"time";d:**********.968749;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:46;a:5:{s:4:"time";d:**********.979431;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:47;a:5:{s:4:"time";d:**********.979705;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:**********.979739;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:**********.980981;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:**********.981077;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:51;a:5:{s:4:"time";d:**********.982292;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:52;a:5:{s:4:"time";d:**********.982363;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:53;a:5:{s:4:"time";d:**********.984678;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:54;a:5:{s:4:"time";d:**********.984697;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:55;a:5:{s:4:"time";d:**********.98752;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:56;a:5:{s:4:"time";d:**********.988218;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:57;a:5:{s:4:"time";d:**********.998045;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:58;a:5:{s:4:"time";d:**********.998245;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:59;a:5:{s:4:"time";d:**********.998256;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:60;a:5:{s:4:"time";d:1748778694.001166;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:61;a:5:{s:4:"time";d:1748778694.018689;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:62;a:5:{s:4:"time";d:1748778694.019169;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:63;a:5:{s:4:"time";d:1748778694.019621;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:64;a:5:{s:4:"time";d:1748778694.019629;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:65;a:5:{s:4:"time";d:1748778694.019638;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:66;a:5:{s:4:"time";d:1748778694.019645;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:67;a:5:{s:4:"time";d:1748778694.020751;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:68;a:5:{s:4:"time";d:1748778694.020883;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:**********.560133;s:3:"end";d:1748778694.027142;s:6:"memory";i:10076896;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2241:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703402;i:4;a:0:{}i:5;i:5773184;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703442;i:4;a:0:{}i:5;i:5773936;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.70346;i:4;a:0:{}i:5;i:5774688;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703475;i:4;a:0:{}i:5;i:5775440;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703489;i:4;a:0:{}i:5;i:5776192;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703502;i:4;a:0:{}i:5;i:5776944;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703514;i:4;a:0:{}i:5;i:5777696;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703528;i:4;a:0:{}i:5;i:5778448;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703541;i:4;a:0:{}i:5;i:5779840;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.703579;i:4;a:0:{}i:5;i:5781936;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.703593;i:4;a:0:{}i:5;i:5781752;}}s:5:"route";s:36:"api/raw-material/accept-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";}";s:7:"request";s:4675:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"f6258dd7-391d-499f-9b63-ffbed7f04220";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"198";s:6:"cookie";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=3ab8f92a20d752fa5d3978c83849421ab2a11af0c904cad41fe3adb22fea9b7ea%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22bkd6TxNtNT7O31PGJocpy0O9164T3Z9V%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683c3ec5a71c6";s:16:"X-Debug-Duration";s:3:"462";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683c3ec5a71c6";s:10:"Set-Cookie";s:204:"_csrf=e2df7c27ae1d0d22935c481c2426b57b927b8ed5bce4fcb73f75697bcc74213da%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%222DS_cyDfV0Fc4-oSNe7x6-3g66EXxpoS%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:36:"api/raw-material/accept-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:198:"{
    "supplier_id": 1,
    "description": "Поставка материалов",
    "materials": [
        {
            "material_id": 4,
            "quantity": 10
        }
    ]
}
";s:7:"Decoded";a:3:{s:11:"supplier_id";i:1;s:11:"description";s:37:"Поставка материалов";s:9:"materials";a:1:{i:0;a:2:{s:11:"material_id";i:4;s:8:"quantity";i:10;}}}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"f6258dd7-391d-499f-9b63-ffbed7f04220";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"198";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=3ab8f92a20d752fa5d3978c83849421ab2a11af0c904cad41fe3adb22fea9b7ea%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22bkd6TxNtNT7O31PGJocpy0O9164T3Z9V%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"54908";s:12:"REDIRECT_URL";s:37:"/api/raw-material/accept-raw-material";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:37:"/api/raw-material/accept-raw-material";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.522892;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"ijocj4ons9v73rb97jao7352v8c6b4cn";s:5:"_csrf";s:130:"3ab8f92a20d752fa5d3978c83849421ab2a11af0c904cad41fe3adb22fea9b7ea:2:{i:0;s:5:"_csrf";i:1;s:32:"bkd6TxNtNT7O31PGJocpy0O9164T3Z9V";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2139:"a:5:{s:2:"id";i:6;s:8:"identity";a:8:{s:2:"id";s:1:"6";s:8:"username";s:12:"'raw_keeper'";s:9:"full_name";s:20:"'Hom ashyo ishchisi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";s:8:"password";s:62:"'$2y$13$bDj/q/7SKFwJIrGKBX7Z8O5TvDtnhjH0Q9YZKxl858G.7vVP8/a.m'";s:10:"created_at";s:21:"'2025-03-16 16:22:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:10:"raw_keeper";a:7:{s:4:"type";i:1;s:4:"name";s:10:"raw_keeper";s:11:"description";s:10:"Raw keeper";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683c3ec5a71c6";s:3:"url";s:50:"http://silver/api/raw-material/accept-raw-material";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.522892;s:10:"statusCode";i:200;s:8:"sqlCount";i:31;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9987696;s:14:"processingTime";d:0.***************;}s:10:"exceptions";a:0:{}}