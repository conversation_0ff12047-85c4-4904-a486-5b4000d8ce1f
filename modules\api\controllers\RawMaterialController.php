<?php

namespace app\modules\api\controllers;

use app\common\models\MaterialStatusGroup;
use Yii;
use app\common\models\Invoice;
use app\common\models\Material;
use app\common\models\Supplier;
use app\common\models\Tracking;
use app\common\models\ApiResponse;
use yii\web\ForbiddenHttpException;
use app\common\models\InvoiceDetail;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\modules\api\models\IncomeRawMaterialForm;
use app\common\models\MaterialStatus;
use app\common\models\ActionLogger;
use app\modules\api\models\SendToProductionForm;
use app\modules\api\models\SendToMaterialDefectForm;
use app\modules\api\models\ReturnMaterialForm;
use app\modules\api\models\UpdateReturnMaterialForm;
use app\modules\api\services\rawMaterial\MaterialReturnService;
use app\modules\api\services\rawMaterial\MaterialReturnManagementService;
use app\common\models\MaterialDefect;
use app\common\models\Product;

/**
 * RawMaterialController implements the CRUD actions for RawMaterial model.
 */
class RawMaterialController extends BaseController
{
        public function beforeAction($action)
        {
            if (!parent::beforeAction($action)) {
                return false;
            }

            try {
                if (!Yii::$app->user->can('raw_keeper')) {
                    throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
                }
            } catch (ForbiddenHttpException $e) {
                Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                Yii::$app->response->data = ApiResponse::response(
                    $e->getMessage(),
                    null,
                    403
                );
                Yii::$app->response->statusCode = 403;
                return false;
            }

            return true;
        }


        public function actionMaterials(){
            if(Yii::$app->request->isGet){
                $materials = Material::find()
                    ->select(['material.id', 'material.name', 'material.unit_type'])
                    ->leftJoin('material_storage', 'material_storage.material_id = material.id')
                    ->where([
                        'or',
                        ['material_storage.deleted_at' => null], // записи с deleted_at IS NULL
                        // ['material_storage.material_id' => null]  // записи, где material_storage отсутствует
                    ])
                    ->groupBy(['material.id', 'material.name', 'material.unit_type'])
                    ->asArray()
                    ->all();

                // Добавляем название единицы измерения для каждого материала
                foreach ($materials as &$material) {
                    $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
                }

                $suppliers = Supplier::find()
                    ->select(['id', 'full_name'])
                    ->where(['deleted_at' => null])
                    ->asArray()
                    ->all();

                return ApiResponse::response('Success', [
                    'materials' => $materials,
                    'suppliers' => $suppliers
                ]);
            } else {
                return ApiResponse::response(
                    'Неверные данные',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        public function actionIndex()
        {
            $today = date('Y-m-d');

            $incomeSql = "
            SELECT
                m.name as material_name,
                CASE
                    WHEN SUM(id.quantity) = FLOOR(SUM(id.quantity))
                    THEN CAST(CAST(SUM(id.quantity) AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING '0' FROM CAST(SUM(id.quantity) AS TEXT))
                END as quantity,
                CASE
                    WHEN id.price = FLOOR(id.price)
                    THEN CAST(CAST(id.price AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING '0' FROM CAST(id.price AS TEXT))
                END as price,
                CASE
                    WHEN SUM(id.quantity * id.price) = FLOOR(SUM(id.quantity * id.price))
                    THEN CAST(CAST(SUM(id.quantity * id.price) AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING '0' FROM CAST(SUM(id.quantity * id.price) AS TEXT))
                END as total_amount,
                m.unit_type
            FROM invoice_detail id
            JOIN invoice i ON i.id = id.invoice_id
            JOIN material m ON m.id = id.material_id
            WHERE DATE(i.created_at) = :today
            AND id.deleted_at IS NULL
            GROUP BY m.id, m.name, id.price, m.unit_type
            ORDER BY m.name
        ";

            $productionSql = "
                SELECT
                    m.name as material_name,
                    CASE
                        WHEN SUM(mp.quantity) = FLOOR(SUM(mp.quantity))
                        THEN CAST(CAST(SUM(mp.quantity) AS INTEGER) AS TEXT)
                        ELSE TRIM(TRAILING '0' FROM CAST(SUM(mp.quantity) AS TEXT))
                    END as quantity,
                    m.unit_type
                FROM material_production mp
                JOIN material m ON m.id = mp.material_id
                WHERE DATE(mp.created_at) = :today
                GROUP BY m.id, m.name, m.unit_type
                ORDER BY m.name
            ";



            $defectSql = "
                    SELECT
                        m.name as material_name,
                        CASE
                            WHEN SUM(md.quantity) = FLOOR(SUM(md.quantity))
                            THEN CAST(CAST(SUM(md.quantity) AS INTEGER) AS TEXT)
                            ELSE TRIM(TRAILING '0' FROM CAST(SUM(md.quantity) AS TEXT))
                        END as quantity,
                        m.unit_type
                    FROM material_defect md
                    JOIN material m ON m.id = md.material_id
                    WHERE DATE(md.created_at) = :today and md.deleted_at IS NULL
                    GROUP BY m.id, m.name, m.unit_type
                    ORDER BY m.name
                ";




            $balanceSql = "
                WITH LastPrice AS (
                    SELECT
                        id.material_id,
                        id.price,
                        i.created_at,
                        ROW_NUMBER() OVER (PARTITION BY id.material_id ORDER BY i.created_at DESC) as rn
                    FROM invoice_detail id
                    JOIN invoice i ON i.id = id.invoice_id
                    WHERE id.deleted_at IS NULL
                ),
                MaterialStorageAggregated AS (
                    SELECT
                        ms.material_id,
                        SUM(ms.quantity) as total_quantity
                    FROM material_storage ms
                    WHERE ms.deleted_at IS NULL AND ms.quantity > 0
                    GROUP BY ms.material_id
                )
                SELECT
                    m.id as material_id,
                    m.name as material_name,
                    CASE
                        WHEN COALESCE(msa.total_quantity, 0) = FLOOR(COALESCE(msa.total_quantity, 0))
                        THEN CAST(CAST(COALESCE(msa.total_quantity, 0) AS INTEGER) AS TEXT)
                        ELSE TRIM(TRAILING '0' FROM CAST(COALESCE(msa.total_quantity, 0) AS TEXT))
                    END as quantity,
                    CASE
                        WHEN COALESCE(lp.price, 0) = FLOOR(COALESCE(lp.price, 0))
                        THEN CAST(CAST(COALESCE(lp.price, 0) AS INTEGER) AS TEXT)
                        ELSE TRIM(TRAILING '0' FROM CAST(COALESCE(lp.price, 0) AS TEXT))
                    END as price,
                    CASE
                        WHEN COALESCE(msa.total_quantity * lp.price, 0) = FLOOR(COALESCE(msa.total_quantity * lp.price, 0))
                        THEN CAST(CAST(COALESCE(msa.total_quantity * lp.price, 0) AS INTEGER) AS TEXT)
                        ELSE TRIM(TRAILING '0' FROM CAST(COALESCE(msa.total_quantity * lp.price, 0) AS TEXT))
                    END as total_amount,
                    m.unit_type
                FROM material m
                LEFT JOIN MaterialStorageAggregated msa ON m.id = msa.material_id
                LEFT JOIN LastPrice lp ON m.id = lp.material_id AND lp.rn = 1
                WHERE m.deleted_at IS NULL
                ORDER BY m.name
            ";


            $balanceTotalsSql = "
            WITH LastPrice AS (
                SELECT
                    id.material_id,
                    id.price,
                    i.created_at,
                    ROW_NUMBER() OVER (PARTITION BY id.material_id ORDER BY i.created_at DESC) as rn
                FROM invoice_detail id
                JOIN invoice i ON i.id = id.invoice_id
                WHERE id.deleted_at IS NULL
            )
            SELECT
                CASE
                    WHEN SUM(COALESCE(ms.quantity, 0)) = FLOOR(SUM(COALESCE(ms.quantity, 0)))
                    THEN CAST(CAST(SUM(COALESCE(ms.quantity, 0)) AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING '0' FROM CAST(SUM(COALESCE(ms.quantity, 0)) AS TEXT))
                END as total_quantity,
                CASE
                    WHEN SUM(COALESCE(ms.quantity * COALESCE(lp.price, 0), 0)) = FLOOR(SUM(COALESCE(ms.quantity * COALESCE(lp.price, 0), 0)))
                    THEN CAST(CAST(SUM(COALESCE(ms.quantity * COALESCE(lp.price, 0), 0)) AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING '0' FROM CAST(SUM(COALESCE(ms.quantity * COALESCE(lp.price, 0), 0)) AS TEXT))
                END as total_sum
            FROM material_storage ms
            LEFT JOIN LastPrice lp ON ms.material_id = lp.material_id AND lp.rn = 1
            WHERE ms.deleted_at IS NULL
        ";



            $income = Yii::$app->db->createCommand($incomeSql)
                ->bindValue(':today', $today)
                ->queryAll();



            // Добавляем название единицы измерения для каждого материала
            foreach ($income as &$item) {
                $item['unit_type_name'] = Material::getUnitTypeName($item['unit_type']);
            }

            $production = Yii::$app->db->createCommand($productionSql)
                ->bindValue(':today', $today)
                ->queryAll();

            // Добавляем название единицы измерения для каждого материала
            foreach ($production as &$item) {
                $item['unit_type_name'] = Material::getUnitTypeName($item['unit_type']);
            }

            $defects = Yii::$app->db->createCommand($defectSql)
                ->bindValue(':today', $today)
                ->queryAll();

            // Добавляем название единицы измерения для каждого материала
            foreach ($defects as &$item) {
                $item['unit_type_name'] = Material::getUnitTypeName($item['unit_type']);
            }

            $balance = Yii::$app->db->createCommand($balanceSql)->queryAll();

            // Добавляем название единицы измерения для каждого материала в балансе
            foreach ($balance as &$item) {
                $item['unit_type_name'] = Material::getUnitTypeName($item['unit_type']);
            }

            $balanceTotals = Yii::$app->db->createCommand($balanceTotalsSql)->queryOne();


            return ApiResponse::response('Движение материалов за сегодня', [
                'income_materials' => $income,
                'production_materials' => $production,
                'defect_materials' => $defects,
                'balance_materials' => [
                    'total_quantity' => $balanceTotals['total_quantity'],
                    'total_sum' => $balanceTotals['total_sum'],
                    'items' => $balance
                ]
            ]);
        }


        public function actionAcceptRawMaterial()
        {
            if(Yii::$app->request->isGet){
                    $date = Yii::$app->request->get('date') ?: date('Y-m-d');
                    $query = Invoice::find()
                    ->select([
                        'invoice.id',
                        'invoice.created_at',
                        'supplier.full_name as supplier_name',
                        new \yii\db\Expression('CASE
                            WHEN tracking.accepted_at IS NOT NULL
                            AND invoice.accepted_at IS NOT NULL
                            and invoice.accept_user_id is not null
                            and invoice.deleted_at is null
                            AND tracking.deleted_at IS NULL
                            THEN true
                            ELSE false
                        END as status')
                    ])
                    ->join('LEFT JOIN', 'supplier', 'supplier.id = invoice.supplier_id')
                    ->join('LEFT JOIN', 'tracking', 'tracking.process_id = invoice.id AND tracking.progress_type = ' . Tracking::TYPE_MATERIAL_INCOME)
                    ->where(['and',
                        ['invoice.deleted_at' => null],
                        ['DATE(invoice.created_at)' => $date]
                    ])
                    ->orderBy(['invoice.created_at' => SORT_DESC]);

                $invoices = $query->asArray()->all();

                foreach ($invoices as &$invoice) {
                    $details = InvoiceDetail::find()
                        ->select([
                            'material.name as material_name',
                            'invoice_detail.quantity',
                            'invoice_detail.price as unit_price',
                            '(invoice_detail.price * invoice_detail.quantity) as total_price',
                            'material.unit_type',
                            new \yii\db\Expression('CASE
                                WHEN material.unit_type = 1 THEN \'Штука\'
                                WHEN material.unit_type = 2 THEN \'Килограмм\'
                                WHEN material.unit_type = 3 THEN \'Литр\'
                                ELSE \'Неизвестно\'
                            END as unit_type_name')
                        ])
                        ->join('LEFT JOIN', 'material', 'material.id = invoice_detail.material_id')
                        ->where([
                            'invoice_detail.invoice_id' => $invoice['id'],
                            'invoice_detail.deleted_at' => null
                        ])
                        ->asArray()
                        ->all();

                    $invoice['materials'] = $details;
                    $invoice['total_sum'] = array_sum(array_column($details, 'total_price'));
                }

                return ApiResponse::response('Success', [
                    'invoices' => $invoices
                ]);
            }

            $model = new IncomeRawMaterialForm();
            $model->load(Yii::$app->request->post(), '');

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $invoice = new Invoice();
                    $prefix = date('Ymd');
                    $random = bin2hex(random_bytes(4));
                    $invoice->invoice_number = sprintf('%s-%s', $prefix, strtoupper($random));
                    $invoice->supplier_id = (int)$model->supplier_id;
                    $invoice->description = $model->description;
                    $invoice->source = Invoice::SOURCE_MOBILE;
                    $invoice->created_at = date('Y-m-d H:i:s');
                    $invoice->total_amount = 0;


                    if (!$invoice->save()) {
                        throw new \Exception('Invoice save error: ' . json_encode($invoice->getErrors()));
                    }

                    foreach ($model->materials as $material) {
                        $invoiceDetail = new InvoiceDetail();
                        $invoiceDetail->invoice_id = $invoice->id;
                        $invoiceDetail->material_id = $material['material_id'];
                        $invoiceDetail->price = null;
                        $invoiceDetail->quantity = $material['quantity'];
                        $invoiceDetail->remainder_quantity = $material['quantity'];
                        $invoiceDetail->created_at = date('Y-m-d H:i:s');

                        if (!$invoiceDetail->save()) {
                            throw new \Exception('Invoice detail save error');
                        }


                        $storage = MaterialStorage::find()
                        ->where(['material_id' => $material['material_id']])
                        ->andWhere(['>=', 'updated_at', date('Y-m-d 00:00:00')])
                        ->andWhere(['<=', 'updated_at', date('Y-m-d 23:59:59')])
                        ->one();

                        if (!$storage) {
                            $storage = new MaterialStorage();
                            $storage->material_id = $material['material_id'];
                            $storage->created_at = date('Y-m-d H:i:s');
                            $storage->quantity = 0;
                        }

                        $storage->quantity += $material['quantity'];
                        $storage->updated_at = date('Y-m-d H:i:s');

                        if (!$storage->save()) {
                            throw new \Exception('Ошибка сохранения склада: ' . json_encode($storage->errors));
                        }

                        $history = new MaterialStorageHistory();
                        $history->material_storage_id = $storage->id;
                        $history->material_id = $material['material_id'];
                        $history->quantity = $material['quantity'];
                        $history->created_at = date('Y-m-d H:i:s');
                        $history->add_user_id = Yii::$app->user->id;
                        $history->type = MaterialStorageHistory::TYPE_INCOME;
                        $history->invoice_detail_id = $invoiceDetail->id;

                        if (!$history->save()) {
                            throw new \Exception('Ошибка сохранения истории: ' . json_encode($history->errors));
                        }

                    }

                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_MATERIAL_INCOME;
                    $tracking->process_id = $invoice->id;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->accepted_at = null;

                    if (!$tracking->save()) {
                        throw new \Exception('Tracking save error');
                    }

                    ActionLogger::actionLog(
                        'accept_raw_material',
                        'invoice',
                        null,
                        [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'supplier_id' => $invoice->supplier_id,
                            'total_amount' => $invoice->total_amount,
                            'materials_count' => count($model->materials),
                            'materials' => array_map(function($material) {
                                return [
                                    'material_id' => $material['material_id'],
                                    'quantity' => $material['quantity']
                                ];
                            }, $model->materials)
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response(
                        'Materials received successfully',
                        [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number
                        ]
                    );

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        'Failed to accept materials',
                        ['error' => $e->getMessage()],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            } else {
                return ApiResponse::response(
                    $model->getErrors(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }


        public function actionUpdateRawMaterial()
        {
            if (Yii::$app->request->isGet) {
                $invoice_id = Yii::$app->request->get('invoice_id');

                if (!$invoice_id) {
                    return ApiResponse::response(
                        'ID накладной обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }


                $invoice = Invoice::find()
                    ->select([
                        'invoice.*',
                        'supplier.full_name as supplier_name'
                    ])
                    ->leftJoin('supplier', 'supplier.id = invoice.supplier_id')
                    ->where(['invoice.id' => $invoice_id])
                    ->andWhere(['invoice.accepted_at' => null])
                    ->andWhere(['invoice.accept_user_id' => null])
                    ->andWhere(['invoice.deleted_at' => null])
                    ->asArray()
                    ->one();

                if (!$invoice) {
                    return ApiResponse::response(
                        Yii::t('app', 'invoice not found or accepted already'),
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }

                $materials = InvoiceDetail::find()
                    ->select([
                        'invoice_detail.*',
                        'material.name as material_name',
                        'material.unit_type'
                    ])
                    ->leftJoin('material', 'material.id = invoice_detail.material_id')
                    ->where(['invoice_id' => $invoice_id])
                    ->andWhere(['IS', 'invoice_detail.deleted_at', null])
                    ->asArray()
                    ->all();

                // Добавляем название единицы измерения для каждого материала
                foreach ($materials as &$material) {
                    $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
                }

                return ApiResponse::response('Данные накладной', [
                    'invoice' => $invoice,
                    'materials' => $materials
                ]);
            }

            if (Yii::$app->request->isPost) {
                $invoice_id = Yii::$app->request->post('invoice_id');
                if (!$invoice_id) {
                    return ApiResponse::response(
                        'ID накладной обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $invoice = Invoice::find()
                    ->where(['id' => $invoice_id])
                    ->andWhere(['accepted_at' => null])
                    ->andWhere(['deleted_at' => null])
                    ->andWhere(['accept_user_id' => null])
                    ->one();

                if (!$invoice) {
                    return ApiResponse::response(
                        Yii::t('app', 'invoice not found or accepted already'),
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }

                $model = new IncomeRawMaterialForm();
                $model->load(Yii::$app->request->post(), '');

                if ($model->validate()) {
                    $transaction = Yii::$app->db->beginTransaction();
                    try {
                        $invoice = Invoice::findOne($invoice_id);
                        if (!$invoice) {
                            throw new \Exception('Накладная не найдена');
                        }

                        $invoice->supplier_id = (int)$model->supplier_id;
                        $invoice->description = $model->description;

                        $existingDetails = InvoiceDetail::find()
                            ->where(['invoice_id' => $invoice_id])
                            ->andWhere(['deleted_at' => null])
                            ->indexBy('material_id')
                            ->all();

                        $updatedMaterialIds = [];

                        foreach ($model->materials as $material) {
                            $materialId = $material['material_id'];
                            $updatedMaterialIds[] = $materialId;

                            if (isset($existingDetails[$materialId])) {
                                $invoiceDetail = $existingDetails[$materialId];
                                $invoiceDetail->price = $material['price'] ?? null;
                                $invoiceDetail->quantity = $material['quantity'];
                                $invoiceDetail->remainder_quantity = $material['quantity'];

                                if (!$invoiceDetail->save()) {
                                    throw new \Exception('Ошибка сохранения деталей накладной');
                                }
                            } else {
                                $invoiceDetail = new InvoiceDetail();
                                $invoiceDetail->invoice_id = $invoice->id;
                                $invoiceDetail->material_id = $materialId;
                                $invoiceDetail->price = $material['price'] ?? null;
                                $invoiceDetail->quantity = $material['quantity'];
                                $invoiceDetail->remainder_quantity = $material['quantity'];
                                $invoiceDetail->created_at = date('Y-m-d H:i:s');

                                if (!$invoiceDetail->save()) {
                                    throw new \Exception('Ошибка сохранения деталей накладной');
                                }
                            }

                        }

                        foreach ($existingDetails as $materialId => $detail) {
                            if (!in_array($materialId, $updatedMaterialIds)) {
                                $detail->deleted_at = date('Y-m-d H:i:s');
                                if (!$detail->save()) {
                                    throw new \Exception('Ошибка удаления деталей накладной');
                                }
                            }
                        }

                        $invoice->total_amount = 0;
                        if (!$invoice->save()) {
                            throw new \Exception('Ошибка обновления накладной');
                        }

                        ActionLogger::actionLog(
                            'update_raw_material',
                            'invoice',
                            $invoice->id,
                            [
                                'invoice_number' => $invoice->invoice_number,
                                'supplier_id' => $invoice->supplier_id,
                                'total_amount' => $invoice->total_amount,
                                'updated_materials' => array_map(function($material) {
                                    return [
                                        'material_id' => $material['material_id'],
                                        'quantity' => $material['quantity']
                                    ];
                                }, $model->materials)
                            ]
                        );

                        $transaction->commit();
                        return ApiResponse::response('Накладная успешно обновлена');

                    } catch (\Exception $e) {
                        $transaction->rollBack();
                        return ApiResponse::response(
                            'Ошибка при обновлении накладной',
                            ['error' => $e->getMessage()],
                            ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                        );
                    }
                } else {
                    return ApiResponse::response(
                        'Ошибка валидации',
                        $model->getErrors(),
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }



        public function actionDeleteRawMaterial()
        {
            if (Yii::$app->request->isPost) {
                $invoice_id = Yii::$app->request->post('invoice_id');

                if (!$invoice_id) {
                    return ApiResponse::response(
                        'ID накладной обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $invoice = Invoice::find()
                    ->where(['id' => $invoice_id])
                    ->andWhere(['accepted_at' => null])
                    ->andWhere(['accept_user_id' => null])
                    ->andWhere(['deleted_at' => null])
                    ->one();

                if (!$invoice) {
                    return ApiResponse::response(
                        'Накладная не найдена',
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }

                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $invoice = Invoice::findOne($invoice_id);
                    if (!$invoice) {
                        throw new \Exception('Накладная не найдена');
                    }

                    $invoiceDetails = InvoiceDetail::find()
                        ->where(['invoice_id' => $invoice_id])
                        ->andWhere(['deleted_at' => null])
                        ->all();

                    foreach ($invoiceDetails as $detail) {
                        // Получаем все записи склада для данного материала, отсортированные по дате создания (от старых к новым)
                        $materialStorages = MaterialStorage::find()
                            ->where(['material_id' => $detail->material_id])
                            ->andWhere(['>', 'quantity', 0])
                            ->andWhere(['deleted_at' => null])
                            ->orderBy(['created_at' => SORT_ASC])
                            ->all();

                        if (empty($materialStorages)) {
                            throw new \Exception('Материал не найден на складе');
                        }

                        // Проверяем общее количество материала на складе
                        $totalAvailable = array_sum(array_column($materialStorages, 'quantity'));
                        if ($totalAvailable < $detail->quantity) {
                            throw new \Exception('Недостаточно материала на складе для удаления накладной. Доступно: ' . $totalAvailable . ', Требуется: ' . $detail->quantity);
                        }

                        $remainingQuantity = $detail->quantity;

                        // Уменьшаем количество материала начиная с самых старых записей
                        foreach ($materialStorages as $storage) {
                            if ($remainingQuantity <= 0) {
                                break; // Все необходимое количество уже удалено
                            }

                            $quantityToRemove = min($storage->quantity, $remainingQuantity);
                            $storage->quantity -= $quantityToRemove;
                            $storage->updated_at = date('Y-m-d H:i:s');

                            if (!$storage->save()) {
                                throw new \Exception('Ошибка сохранения склада: ' . json_encode($storage->errors));
                            }

                            // Создаем запись в истории склада
                            $history = new MaterialStorageHistory();
                            $history->material_storage_id = $storage->id;
                            $history->material_id = $detail->material_id;
                            $history->quantity = $quantityToRemove;
                            $history->created_at = date('Y-m-d H:i:s');
                            $history->add_user_id = Yii::$app->user->id;
                            $history->type = MaterialStorageHistory::TYPE_OUTCOME;
                            $history->invoice_detail_id = $detail->id;

                            if (!$history->save()) {
                                throw new \Exception('Ошибка сохранения истории: ' . json_encode($history->errors));
                            }

                            $remainingQuantity -= $quantityToRemove;
                        }

                        // Проверяем, что все количество было успешно удалено
                        if ($remainingQuantity > 0) {
                            throw new \Exception('Не удалось удалить все необходимое количество материала со склада');
                        }

                        // Помечаем деталь накладной как удаленную
                        $detail->deleted_at = date('Y-m-d H:i:s');
                        if (!$detail->save()) {
                            throw new \Exception('Ошибка при удалении деталей накладной');
                        }
                    }

                    $tracking = Tracking::find()
                    ->where(['process_id' => $invoice_id])
                    ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
                    ->andWhere(['deleted_at' => null])
                    ->andWhere(['accepted_at' => null])
                    ->one();

                    if (!$tracking) {
                        throw new \Exception('Tracking не найден');
                    }

                    $tracking->deleted_at = date('Y-m-d H:i:s');
                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при обновлении tracking');
                    }

                    $invoice->deleted_at = date('Y-m-d H:i:s');
                    if (!$invoice->save()) {
                        throw new \Exception('Ошибка при удалении накладной');
                    }

                    ActionLogger::actionLog(
                        'delete_raw_material',
                        'invoice',
                        $invoice->id,
                        [
                            'invoice_number' => $invoice->invoice_number,
                            'supplier_id' => $invoice->supplier_id,
                            'total_amount' => $invoice->total_amount,
                            'materials' => array_map(function($detail) {
                                return [
                                    'material_id' => $detail->material_id,
                                    'quantity' => $detail->quantity,
                                    'price' => $detail->price
                                ];
                            }, $invoiceDetails)
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response('Накладная успешно удалена');

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        'Ошибка при удалении накладной',
                        ['error' => $e->getMessage()],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }



        public function actionSendToProduction()
        {
            if (Yii::$app->request->isGet) {
                $product_id = Yii::$app->request->get('product_id');
                $date = Yii::$app->request->get('date') ?: date('Y-m-d');

                $query = MaterialStatusGroup::find()
                ->select([
                    'material_status_group.id',
                    'material_status_group.add_user_id',
                    'material_status_group.created_at',
                    'u.username as user_name',
                    new \yii\db\Expression('CASE
                        WHEN material_status_group.accepted_user_id IS NOT NULL
                        AND material_status_group.accepted_at IS NOT NULL
                        THEN true
                        ELSE false
                    END as is_accepted'),
                    new \yii\db\Expression('CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                        THEN true
                        ELSE false
                    END as is_tracked')
                ])
                ->leftJoin('users u', 'u.id = material_status_group.add_user_id')
                ->leftJoin('tracking t', 't.process_id = material_status_group.id AND t.progress_type = ' . Tracking::TYPE_MATERIAL_OUTCOME)
                ->where(['DATE(material_status_group.created_at)' => $date])
                ->andWhere(['material_status_group.deleted_at' => null])
                ->andWhere(['material_status_group.status' => MaterialStatusGroup::STATUS_IN_PRODUCTION])
                ->orderBy(['material_status_group.created_at' => SORT_DESC]);

                $productionGroups = $query->asArray()->all();

                foreach ($productionGroups as &$group) {
                    $materials = MaterialStatus::find()
                        ->select([
                            'material_status.id',
                            'm.name as material_name',
                            new \yii\db\Expression('
                            CASE
                                WHEN material_status.quantity = FLOOR(material_status.quantity) THEN CAST(CAST(material_status.quantity AS INTEGER) AS TEXT)
                                ELSE TRIM(TRAILING \'0\' FROM CAST(material_status.quantity AS TEXT))
                            END as quantity
                            '),
                            'm.unit_type',
                            'COALESCE(id.price, 0) as unit_price',
                            'COALESCE(id.price * material_status.quantity, 0) as total_price'
                        ])
                        ->leftJoin('material m', 'material_status.material_id = m.id')
                        ->leftJoin(['id' => '(SELECT material_id, price FROM invoice_detail WHERE deleted_at IS NULL AND id IN (SELECT MAX(id) FROM invoice_detail WHERE deleted_at IS NULL GROUP BY material_id))'],
                            'id.material_id = material_status.material_id')
                        ->where([
                            'material_status.status_group_id' => $group['id'],
                            'material_status.deleted_at' => null
                        ])
                        ->asArray()
                        ->all();

                    // Добавляем название единицы измерения для каждого материала
                    foreach ($materials as &$material) {
                        $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
                    }

                    $group['materials'] = $materials;
                    $group['total_sum'] = array_sum(array_column($materials, 'total_price'));
                }

                $productionMaterials = $productionGroups;

                $productMaterials = [];
                if ($product_id) {
                    $productMaterials = Material::find()
                        ->select([
                            'material.id',
                            'material.name',
                            'material.description'
                        ])
                        ->innerJoin('product_ingredients pi', 'pi.material_id = material.id')
                        ->innerJoin('material_storage ms', 'ms.material_id = material.id')
                        ->where(['pi.product_id' => $product_id])
                        ->andWhere(['OR',
                            ['pi.end_date' => null],
                            ['>', 'pi.end_date', new \yii\db\Expression('NOW()')],
                        ])
                        ->andWhere(['ms.deleted_at' => null])
                        ->andWhere(['>', 'ms.quantity', 0])
                        ->groupBy(['material.id', 'material.name'])
                        ->asArray()
                        ->all();
                }

                $products = Product::find()->where(['deleted_at' => null])->asArray()->all();
                $result = array_map(function($product) {
                    return [
                        'id' => $product['id'],
                        'name' => $product['name']
                    ];
                }, $products);

                return ApiResponse::response('Материалы для производства', [
                    'production_materials' => $productionMaterials,
                    'product_materials' => $productMaterials,
                    'products' => $result
                ]);
            }

            if (Yii::$app->request->isPost) {
                $model = new SendToProductionForm();
                $model->load(Yii::$app->request->post(), '');

                if ($model->validate()) {
                    $transaction = Yii::$app->db->beginTransaction();
                    try {
                        $group = new MaterialStatusGroup();
                        $group->add_user_id = Yii::$app->user->id;
                        $group->status = MaterialStatusGroup::STATUS_IN_PRODUCTION;
                        $group->created_at = date('Y-m-d H:i:s');

                        if (!$group->save()) {
                            throw new \Exception(Yii::t('app', 'Error saving material group'));
                        }

                        $trackingIds = [];
                        foreach ($model->materials as $material) {
                            if (!isset($material['material_id']) || !isset($material['quantity'])) {
                                throw new \Exception(Yii::t('app', 'Invalid material data: missing material_id or quantity'));
                            }

                            $materialModel = Material::findOne($material['material_id']);
                            if (!$materialModel) {
                                throw new \Exception(Yii::t('app', 'Material with ID #{material_id} not found in database', [
                                    'material_id' => $material['material_id']
                                ]));
                            }
                            $materialName = $materialModel->name;


                            $materialStorages = MaterialStorage::find()
                                ->where(['material_id' => $material['material_id']])
                                ->andWhere(['>', 'quantity', 0])
                                ->andWhere(['deleted_at' => null])
                                ->orderBy(['created_at' => SORT_ASC])
                                ->all();

                            if (empty($materialStorages)) {
                                throw new \Exception(Yii::t('app', 'Material "{material_name}" not found in stock', [
                                    'material_name' => $materialName
                                ]));
                            }

                            $quantities = array_column($materialStorages, 'quantity');
                            if (empty($quantities) || !is_array($quantities)) {
                                throw new \Exception(Yii::t('app', 'Invalid quantity data for material "{material_name}"', [
                                    'material_name' => $materialName
                                ]));
                            }

                            $totalAvailable = array_sum($quantities);

                            if ($totalAvailable < $material['quantity']) {
                                throw new \Exception(Yii::t('app', 'Not enough material "{material_name}" in stock. Available: {available}, Required: {required}', [
                                    'material_name' => $materialName,
                                    'available' => $totalAvailable,
                                    'required' => $material['quantity'],
                                ]));
                            }


                            $materialStatus = new MaterialStatus();
                            $materialStatus->material_id = $material['material_id'];
                            $materialStatus->quantity = $material['quantity'];
                            $materialStatus->status_group_id = $group->id;
                            $materialStatus->created_at = date('Y-m-d H:i:s');

                            if (!$materialStatus->save()) {
                                throw new \Exception(Yii::t('app', 'Error saving material status'));
                            }
                        }

                        $tracking = new Tracking();
                        $tracking->progress_type = Tracking::TYPE_MATERIAL_OUTCOME;
                        $tracking->process_id = $group->id;
                        $tracking->created_at = date('Y-m-d H:i:s');
                        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                        $tracking->accepted_at = null;

                        if (!$tracking->save()) {
                            throw new \Exception(Yii::t('app', 'Error saving tracking'));
                        }

                        $trackingIds[] = $tracking->id;

                        ActionLogger::actionLog(
                            'send_to_production',
                            'material_status',
                            null,
                            [
                                'group_id' => $group->id,
                                'tracking_ids' => $trackingIds,
                                'materials' => array_map(function($material) {
                                    return [
                                        'material_id' => $material['material_id'],
                                        'quantity' => $material['quantity']
                                    ];
                                }, $model->materials)
                            ]
                        );

                        $transaction->commit();
                        return ApiResponse::response(Yii::t('app', 'Materials successfully sent to production'));

                    } catch (\Exception $e) {
                        $transaction->rollBack();
                        return ApiResponse::response(
                            Yii::t('app', 'Error sending materials to production'),
                            ['error' => $e->getMessage()],
                            ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                        );
                    }
                }
            }
        }


        public function actionUpdateSendToProduction()
        {
            if (Yii::$app->request->isGet) {
                $product_id = Yii::$app->request->get('product_id');
                $id = Yii::$app->request->get('group_id');
                if (!$id) {
                    return ApiResponse::response(
                        'ID группы обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $group = MaterialStatusGroup::find()
                    ->select([
                        'material_status_group.id',
                        'material_status_group.add_user_id',
                        'material_status_group.created_at',
                        'material_status_group.status',
                        'u.username as user_name',
                        new \yii\db\Expression('CASE
                            WHEN material_status_group.accepted_user_id IS NOT NULL
                            AND material_status_group.accepted_at IS NOT NULL
                            THEN true
                            ELSE false
                        END as is_accepted'),
                        new \yii\db\Expression('CASE
                            WHEN t.accepted_at IS NOT NULL
                            AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                            THEN true
                            ELSE false
                        END as is_tracked')
                    ])
                    ->leftJoin('users u', 'u.id = material_status_group.add_user_id')
                    ->leftJoin('tracking t', 't.process_id = material_status_group.id AND t.progress_type = ' . Tracking::TYPE_MATERIAL_OUTCOME)
                    ->where([
                        'material_status_group.id' => $id,
                        'material_status_group.deleted_at' => null
                    ])
                    ->asArray()
                    ->one();

                if (!$group) {
                    return ApiResponse::response(
                        'Группа материалов не найдена',
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }

                $materials = MaterialStatus::find()
                    ->select([
                        'material_status.id',
                        'material_status.material_id',
                        'm.name as material_name',
                        'material_status.quantity',
                        'm.unit_type',
                        'COALESCE(id.price, 0) as unit_price',
                        'COALESCE(id.price * material_status.quantity, 0) as total_price'
                    ])
                    ->leftJoin('material m', 'material_status.material_id = m.id')
                    ->leftJoin(['id' => '(SELECT material_id, price FROM invoice_detail WHERE deleted_at IS NULL AND id IN (SELECT MAX(id) FROM invoice_detail WHERE deleted_at IS NULL GROUP BY material_id))'],
                        'id.material_id = material_status.material_id')
                    ->where([
                        'material_status.status_group_id' => $group['id'],
                        'material_status.deleted_at' => null
                    ])
                    ->orderBy(['material_status.id' => SORT_ASC])
                    ->asArray()
                    ->all();

                $products = Product::find()
                    ->select(['id', 'name'])
                    ->where(['deleted_at' => null])
                    ->asArray()
                    ->all();


                    $productMaterials = [];
                if ($product_id) {
                    $productMaterials = Material::find()
                        ->select([
                            'material.id',
                            'material.name',
                            'material.description'
                        ])
                        ->leftJoin('material_storage ms', 'ms.material_id = material.id')
                        ->innerJoin('product_ingredients pi', 'pi.material_id = material.id')
                        ->where(['pi.product_id' => $product_id])
                        ->andWhere(['OR',
                            ['pi.end_date' => null],
                            ['>', 'pi.end_date', new \yii\db\Expression('NOW()')],
                        ])
                        ->asArray()
                        ->all();
                }

                $products = Product::find()
                    ->select(['id', 'name'])
                    ->where(['deleted_at' => null])
                    ->asArray()
                    ->all();
                $result = array_map(function($product) {
                    return [
                        'id' => $product['id'],
                        'name' => $product['name']
                    ];
                }, $products);

                $result = [
                    'production_materials' => [[
                        'id' => $group['id'],
                        'add_user_id' => $group['add_user_id'],
                        'created_at' => $group['created_at'],
                        'user_name' => $group['user_name'],
                        'is_accepted' => $group['is_accepted'],
                        'is_tracked' => $group['is_tracked'],
                        'materials' => array_map(function($material) {
                            return [
                                'id' => $material['id'],
                                'material_name' => $material['material_name'],
                                'quantity' => $material['quantity'],
                                'unit_type' => $material['unit_type'],
                                'unit_type_name' => Material::getUnitTypeName($material['unit_type']),
                                'unit_price' => $material['unit_price'],
                                'total_price' => $material['total_price']
                            ];
                        }, $materials),
                        'total_sum' => array_sum(array_column($materials, 'total_price'))
                    ]],
                    'products' => $products,
                    'product_materials' => $productMaterials
                ];

                return ApiResponse::response('Успешно', $result);
            }

            if (Yii::$app->request->isPost) {
                $group_id = Yii::$app->request->post('group_id');
                if (!$group_id) {
                    return ApiResponse::response(
                        Yii::t('app', 'Group ID is required'),
                        null,
                        ApiResponse::HTTP_BAD_REQUEST // 400: Некорректный запрос
                    );
                }

                $group = MaterialStatusGroup::findOne([
                    'id' => $group_id,
                    'deleted_at' => null
                ]);
                if (!$group) {
                    return ApiResponse::response(
                        Yii::t('app', 'Group not found'),
                        null,
                        ApiResponse::HTTP_NOT_FOUND // 404: Группа не найдена
                    );
                }

                if ($group->accepted_user_id !== null || $group->accepted_at !== null) {
                    return ApiResponse::response(
                        Yii::t('app', 'Group already confirmed'),
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Группа уже подтверждена
                    );
                }

                $tracking = Tracking::findOne([
                    'process_id' => $group->id,
                    'progress_type' => Tracking::TYPE_MATERIAL_OUTCOME,
                    'deleted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    return ApiResponse::response(
                        Yii::t('app', 'Group already confirmed or tracking not found'),
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Трекинг не найден или группа подтверждена
                    );
                }

                $model = new SendToProductionForm();
                $model->load(Yii::$app->request->post(), '');

                if ($model->validate()) {
                    $transaction = Yii::$app->db->beginTransaction();
                    try {
                        // Проверяем наличие материалов на складе
                        foreach ($model->materials as $material) {
                            if (!isset($material['material_id']) || !isset($material['quantity'])) {
                                return ApiResponse::response(
                                    Yii::t('app', 'Error updating materials'),
                                    ['error' => Yii::t('app', 'Invalid material data: missing material_id or quantity')],
                                    ApiResponse::HTTP_BAD_REQUEST // 400: Некорректные данные
                                );
                            }

                            // Получаем название материала
                            $materialModel = Material::findOne($material['material_id']);
                            if (!$materialModel) {
                                return ApiResponse::response(
                                    Yii::t('app', 'Error updating materials'),
                                    ['error' => Yii::t('app', 'Material with ID #{material_id} not found in database', [
                                        'material_id' => $material['material_id']
                                    ])],
                                    ApiResponse::HTTP_NOT_FOUND // 404: Материал не найден
                                );
                            }
                            $materialName = $materialModel->name;


                            $materialStorages = MaterialStorage::find()
                                ->where(['material_id' => $material['material_id']])
                                ->andWhere(['>', 'quantity', 0])
                                ->andWhere(['deleted_at' => null])
                                ->orderBy(['created_at' => SORT_ASC])
                                ->all();

                            if (empty($materialStorages)) {
                                return ApiResponse::response(
                                    Yii::t('app', 'Error updating materials'),
                                    ['error' => Yii::t('app', 'Material "{material_name}" not found in stock', [
                                        'material_name' => $materialName
                                    ])],
                                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Материал не найден на складе
                                );
                            }

                            $quantities = array_column($materialStorages, 'quantity');
                            if (empty($quantities) || !is_array($quantities)) {
                                return ApiResponse::response(
                                    Yii::t('app', 'Error updating materials'),
                                    ['error' => Yii::t('app', 'Invalid quantity data for material "{material_name}"', [
                                        'material_name' => $materialName
                                    ])],
                                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Некорректные данные о количестве
                                );
                            }

                            $totalAvailable = array_sum($quantities);
                            if ($totalAvailable < $material['quantity']) {
                                return ApiResponse::response(
                                    Yii::t('app', 'Error updating materials'),
                                    ['error' => Yii::t('app', 'Not enough material "{material_name}" in stock. Available: {available}, Required: {required}', [
                                        'material_name' => $materialName,
                                        'available' => $totalAvailable,
                                        'required' => $material['quantity'],
                                    ])],
                                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Недостаточно материала
                                );
                            }

                        }

                        // Получаем существующие записи MaterialStatus для группы
                        $groupMaterials = MaterialStatus::find()
                            ->where([
                                'status_group_id' => $group->id,
                                'deleted_at' => null
                            ])
                            ->all();

                        // Обновляем или "удаляем" существующие материалы
                        foreach ($groupMaterials as $materialStatus) {
                            $found = false;
                            foreach ($model->materials as $newMaterial) {
                                if ($materialStatus->material_id == $newMaterial['material_id']) {
                                    $materialStatus->quantity = $newMaterial['quantity'];
                                    $materialStatus->created_at = date('Y-m-d H:i:s'); // Обновляем дату изменения
                                    if (!$materialStatus->save()) {
                                        throw new \Exception(Yii::t('app', 'Error updating material #{material_id}', [
                                            'material_id' => $materialStatus->material_id
                                        ]));
                                    }
                                    $found = true;
                                    break;
                                }
                            }

                            if (!$found) {
                                $materialStatus->deleted_at = new \yii\db\Expression('NOW()');
                                if (!$materialStatus->save()) {
                                    throw new \Exception(Yii::t('app', 'Error deleting material #{material_id}', [
                                        'material_id' => $materialStatus->material_id
                                    ]));
                                }
                            }
                        }

                        // Добавляем новые материалы, если их не было в группе
                        foreach ($model->materials as $newMaterial) {
                            $exists = MaterialStatus::find()
                                ->where([
                                    'status_group_id' => $group->id,
                                    'material_id' => $newMaterial['material_id'],
                                    'deleted_at' => null
                                ])
                                ->exists();

                            if (!$exists) {
                                $materialStatus = new MaterialStatus();
                                $materialStatus->material_id = $newMaterial['material_id'];
                                $materialStatus->quantity = $newMaterial['quantity'];
                                $materialStatus->status_group_id = $group->id;
                                $materialStatus->created_at = date('Y-m-d H:i:s');
                                if (!$materialStatus->save()) {
                                    throw new \Exception(Yii::t('app', 'Error adding new material #{material_id}', [
                                        'material_id' => $newMaterial['material_id']
                                    ]));
                                }
                            }
                        }

                        // Логируем действие
                        ActionLogger::actionLog(
                            'update_send_to_production',
                            'material_status_group',
                            $group->id,
                            [
                                'materials' => array_map(function($material) {
                                    return [
                                        'material_id' => $material['material_id'],
                                        'quantity' => $material['quantity']
                                    ];
                                }, $model->materials)
                            ]
                        );

                        $transaction->commit();
                        return ApiResponse::response(Yii::t('app', 'Materials successfully updated'));

                    } catch (\Exception $e) {
                        $transaction->rollBack();
                        return ApiResponse::response(
                            Yii::t('app', 'Error updating materials'),
                            ['error' => $e->getMessage()],
                            ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 500: Для непредвиденных ошибок
                        );
                    }
                } else {
                    return ApiResponse::response(
                        Yii::t('app', 'Validation error'),
                        $model->getErrors(),
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY // 422: Ошибка валидации
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        public function actionDeleteSendToProduction()
        {
            if (Yii::$app->request->isPost) {
                $group_id = Yii::$app->request->post('group_id');

                if (!$group_id) {
                    return ApiResponse::response(
                        'ID группы материала обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $group = MaterialStatusGroup::findOne([
                    'id' => $group_id,
                    'deleted_at' => null
                ]);
                if (!$group) {
                    return ApiResponse::response(
                        'Группа материалов не найдена',
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }


                if ($group->accepted_user_id !== null || $group->accepted_at !== null) {
                    return ApiResponse::response(
                        'Группа уже подтверждена',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $tracking = Tracking::findOne([
                    'process_id' => $group->id,
                    'progress_type' => Tracking::TYPE_MATERIAL_OUTCOME,
                    'deleted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    return ApiResponse::response(
                        'Группа уже подтверждена или не найден tracking',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $tracking->deleted_at = date('Y-m-d H:i:s');
                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при обновлении tracking');
                    }

                    $group->deleted_at = date('Y-m-d H:i:s');
                    if (!$group->save()) {
                        throw new \Exception('Ошибка при удалении группы');
                    }

                    $materials = MaterialStatus::updateAll(
                        ['deleted_at' => date('Y-m-d H:i:s')],
                        ['status_group_id' => $group->id, 'deleted_at' => null]
                    );

                    ActionLogger::actionLog(
                        'delete_send_to_production',
                        'material_status_group',
                        $group->id,
                        [
                            'materials_count' => $materials
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response('Группа материалов успешно удалена');

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        'Ошибка при удалении группы материалов',
                        ['error' => $e->getMessage()],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        public function actionAcceptMaterialReturn()
        {
            if (Yii::$app->request->isPost) {
                $groupId = Yii::$app->request->post('group_id');

                if (!$groupId) {
                    return ApiResponse::response(
                        'ID группы возврата обязателен',
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }

                // Определяем тип возврата по статусу группы
                $materialStatusGroup = MaterialStatusGroup::findOne([
                    'id' => $groupId,
                    'deleted_at' => null
                ]);

                if (!$materialStatusGroup) {
                    return ApiResponse::response(
                        'Группа возврата не найдена',
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }

                // Выбираем правильный сервис в зависимости от типа возврата
                if ($materialStatusGroup->status == MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION) {
                    $service = new \app\modules\api\services\rawMaterial\MaterialProductionReturnAcceptService();
                }

                $result = $service->acceptMaterialReturn($groupId);

                if ($result['success']) {
                    return ApiResponse::response($result['message']);
                } else {
                    return ApiResponse::response(
                        'Ошибка при подтверждении возврата материалов',
                        ['error' => $result['message']],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        public function actionReturnMaterial()
        {
            if (Yii::$app->request->isGet) {
                $date = Yii::$app->request->get('date') ?: date('Y-m-d');

                // Получаем возвраты поставщикам
                $returnsToSupplier = $this->getReturnsByType(
                    $date,
                    MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER
                );

                // Получаем возвраты из производства
                $returnsFromProduction = $this->getReturnsByType(
                    $date,
                    MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION
                );

                // Получаем список поставщиков для выпадающего списка
                $suppliers = Supplier::find()
                    ->select(['id', 'full_name'])
                    ->where(['deleted_at' => null])
                    ->asArray()
                    ->all();

                return ApiResponse::response('Возвраты материалов', [
                    'returns_to_supplier' => $returnsToSupplier,
                    'returns_from_production' => $returnsFromProduction,
                    'suppliers' => $suppliers,
                ]);
            }

            if (Yii::$app->request->isPost) {
                $model = new ReturnMaterialForm();
                $model->load(Yii::$app->request->post(), '');

                if ($model->validate()) {
                    $service = new MaterialReturnService();
                    $result = $service->createMaterialReturn($model);

                    if ($result['success']) {
                        return ApiResponse::response($result['message'], [
                            'group_id' => $result['group_id']
                        ]);
                    } else {
                        return ApiResponse::response(
                            'Ошибка при возврате материалов',
                            ['error' => $result['message']],
                            ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                        );
                    }
                } else {
                    return ApiResponse::response(
                        'Ошибка валидации',
                        ['errors' => $model->getErrors()],
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        /**
         * Получение данных возврата материалов для редактирования
         * GET /api/raw-material/return-material/{id}
         */
        public function actionGetReturnMaterial($id)
        {
            if (Yii::$app->request->isGet) {
                $service = new MaterialReturnManagementService();
                $result = $service->getReturnMaterialForUpdate($id);

                if ($result['success']) {
                    return ApiResponse::response('Данные возврата материалов', $result['data']);
                } else {
                    return ApiResponse::response(
                        $result['message'],
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        /**
         * Обновление возврата материалов
         * PUT /api/raw-material/return-material/{id}
         */
        public function actionUpdateReturnMaterial($id)
        {
            if (Yii::$app->request->isPut) {
                $model = new UpdateReturnMaterialForm();
                $model->group_id = $id;
                $model->load(Yii::$app->request->post(), '');

                if ($model->validate()) {
                    $service = new MaterialReturnManagementService();
                    $returnForm = $model->toReturnMaterialForm();
                    $result = $service->updateMaterialReturn($id, $returnForm);

                    if ($result['success']) {
                        return ApiResponse::response($result['message']);
                    } else {
                        return ApiResponse::response(
                            'Ошибка при обновлении возврата материалов',
                            ['error' => $result['message']],
                            ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                        );
                    }
                } else {
                    return ApiResponse::response(
                        'Ошибка валидации',
                        ['errors' => $model->getErrors()],
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        /**
         * Удаление возврата материалов
         * DELETE /api/raw-material/return-material/{id}
         */
        public function actionDeleteReturnMaterial($id)
        {
            if (Yii::$app->request->isDelete) {
                $service = new MaterialReturnManagementService();
                $result = $service->deleteMaterialReturn($id);

                if ($result['success']) {
                    return ApiResponse::response($result['message']);
                } else {
                    return ApiResponse::response(
                        'Ошибка при удалении возврата материалов',
                        ['error' => $result['message']],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }

            return ApiResponse::response(
                'Метод не поддерживается',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        /**
         * Получение возвратов материалов по типу
         *
         * @param string $date Дата для фильтрации
         * @param int $status Статус возврата (STATUS_RETURNED_TO_SUPPLIER или STATUS_RETURNED_FROM_PRODUCTION)
         * @return array Массив групп возврата с материалами
         */
        private function getReturnsByType($date, $status)
        {
            $query = MaterialStatusGroup::find()
                ->select([
                    'material_status_group.id',
                    'material_status_group.add_user_id',
                    'material_status_group.created_at',
                    'material_status_group.supplier_id',
                    'material_status_group.status',
                    'u.username as user_name',
                    'u.full_name as user_full_name',
                    's.full_name as supplier_name',
                    new \yii\db\Expression('CASE
                        WHEN material_status_group.accepted_user_id IS NOT NULL
                        AND material_status_group.accepted_at IS NOT NULL
                        THEN true
                        ELSE false
                    END as is_accepted'),
                    new \yii\db\Expression('CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                        THEN true
                        ELSE false
                    END as is_tracked'),
                    'material_status_group.accepted_at',
                    'au.full_name as accepted_by'
                ])
                ->leftJoin('users u', 'u.id = material_status_group.add_user_id')
                ->leftJoin('users au', 'au.id = material_status_group.accepted_user_id')
                ->leftJoin('supplier s', 's.id = material_status_group.supplier_id')
                ->leftJoin('tracking t', 't.process_id = material_status_group.id AND t.progress_type = ' . Tracking::TYPE_MATERIAL_RETURN)
                ->where(['DATE(material_status_group.created_at)' => $date])
                ->andWhere(['material_status_group.deleted_at' => null])
                ->andWhere(['material_status_group.status' => $status])
                ->orderBy(['material_status_group.created_at' => SORT_DESC]);

            $returnGroups = $query->asArray()->all();

            foreach ($returnGroups as &$group) {
                // Получаем материалы для группы
                $materials = $this->getMaterialsForGroup($group['id']);

                $group['materials'] = $materials;
                $group['total_sum'] = array_sum(array_column($materials, 'total_price'));
                $group['materials_count'] = count($materials);

                // Добавляем специфичную для типа информацию
                if ($status == MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION) {
                    $group['return_type'] = 'from_production';
                } else {
                    $group['return_type'] = 'to_supplier';
                }
            }

            return $returnGroups;
        }

        /**
         * Получение материалов для группы возврата
         *
         * @param int $groupId ID группы возврата
         * @return array Массив материалов с ценами
         */
        private function getMaterialsForGroup($groupId)
        {
            $materials = MaterialStatus::find()
                ->select([
                    'material_status.id',
                    'material_status.material_id',
                    'm.name as material_name',
                    'to_char(material_status.quantity, \'FM999999999.########\') as quantity',
                    'material_status.quantity as raw_quantity',
                    'm.unit_type',
                    'COALESCE(id.price, 0) as unit_price',
                    'to_char(COALESCE(id.price * material_status.quantity, 0), \'FM999999999.########\') as total_price'
                ])
                ->leftJoin('material m', 'material_status.material_id = m.id')
                ->leftJoin(['id' => '(
                    WITH ranked_prices AS (
                        SELECT
                            material_id,
                            price,
                            id,
                            ROW_NUMBER() OVER (
                                PARTITION BY material_id
                                ORDER BY
                                    CASE WHEN price IS NOT NULL AND price > 0 THEN 0 ELSE 1 END,
                                    id DESC
                            ) as rn
                        FROM invoice_detail
                        WHERE deleted_at IS NULL
                    )
                    SELECT material_id, price
                    FROM ranked_prices
                    WHERE rn = 1
                )'], 'id.material_id = material_status.material_id')
                ->where([
                    'material_status.status_group_id' => $groupId,
                    'material_status.deleted_at' => null
                ])
                ->asArray()
                ->all();

            // Добавляем название единицы измерения для каждого материала
            foreach ($materials as &$material) {
                $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
            }

            return $materials;
        }
}