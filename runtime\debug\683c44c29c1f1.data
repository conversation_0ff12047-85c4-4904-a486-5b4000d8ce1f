a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:26991:"a:1:{s:8:"messages";a:33:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.623536;i:4;a:0:{}i:5;i:2611912;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.624336;i:4;a:0:{}i:5;i:2728920;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.624669;i:4;a:0:{}i:5;i:2770128;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.624674;i:4;a:0:{}i:5;i:2770504;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.631414;i:4;a:0:{}i:5;i:3915704;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.636417;i:4;a:0:{}i:5;i:4725960;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.639163;i:4;a:0:{}i:5;i:5115624;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.641511;i:4;a:0:{}i:5;i:5580032;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.64171;i:4;a:0:{}i:5;i:5607424;}i:20;a:6:{i:0;s:55:"Route requested: 'api/raw-material/accept-raw-material'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.642705;i:4;a:0:{}i:5;i:5781080;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.64271;i:4;a:0:{}i:5;i:5782728;}i:22;a:6:{i:0;s:50:"Route to run: api/raw-material/accept-raw-material";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.65094;i:4;a:0:{}i:5;i:6200040;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.679984;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7868464;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.714349;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8072184;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.726546;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8118096;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.730613;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8413904;}i:35;a:6:{i:0;s:55:"User '6' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.735472;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8700552;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.735501;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8701144;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.736998;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8888728;}i:40;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.739662;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8895352;}i:43;a:6:{i:0;s:25:"Checking role: raw_keeper";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.74126;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8898744;}i:44;a:6:{i:0;s:92:"Running action: app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.741281;i:4;a:0:{}i:5;i:8897904;}i:45;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.743254;i:4;a:2:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:96;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9166496;}i:46;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.743775;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9210160;}i:49;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.747351;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9222648;}i:52;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.750554;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9342288;}i:55;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-981A70F0', 1, 'Поставка материалов', 2, '2025-06-01 17:17:06', 0, NULL, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.752502;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9379672;}i:58;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.755362;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9412264;}i:61;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.759048;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9424304;}i:64;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=38)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.761627;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9445248;}i:67;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763242;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9492024;}i:70;a:6:{i:0;s:43:"Model not inserted due to validation error.";i:1;i:4;i:2;s:27:"yii\db\ActiveRecord::insert";i:3;d:**********.765328;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9486784;}i:71;a:6:{i:0;s:21:"Roll back transaction";i:1;i:8;i:2;s:28:"yii\db\Transaction::rollBack";i:3;d:**********.765368;i:4;a:2:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:126;s:8:"function";s:8:"rollBack";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9486848;}}}";s:9:"profiling";s:43180:"a:3:{s:6:"memory";i:9633040;s:4:"time";d:0.15041089057922363;s:8:"messages";a:28:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.68004;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7869968;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.713507;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7872272;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.714381;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074136;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.725814;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8089872;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.726567;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8119960;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.72839;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8121896;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.730639;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418168;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.733217;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8420928;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.737019;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891336;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.739348;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893504;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.73968;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8897960;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.741004;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900064;}i:47;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.743808;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9212400;}i:48;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.747078;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9231784;}i:50;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.747365;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9224888;}i:51;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.749027;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9228656;}i:53;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.750581;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9345664;}i:54;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.751765;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9347648;}i:56;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-981A70F0', 1, 'Поставка материалов', 2, '2025-06-01 17:17:06', 0, NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.752512;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9381176;}i:57;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-981A70F0', 1, 'Поставка материалов', 2, '2025-06-01 17:17:06', 0, NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.75425;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9383816;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.755385;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9414504;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.758592;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9432416;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.759064;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9426544;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.761028;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9430896;}i:65;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=38)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.761645;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9454256;}i:66;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=38)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.762787;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9456272;}i:68;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763261;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9495400;}i:69;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.764915;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9497416;}}}";s:2:"db";s:41933:"a:1:{s:8:"messages";a:26:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.714381;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074136;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.725814;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8089872;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.726567;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8119960;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.72839;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8121896;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.730639;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418168;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.733217;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8420928;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.737019;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891336;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.739348;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893504;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.73968;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8897960;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.741004;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900064;}i:47;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.743808;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9212400;}i:48;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.747078;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9231784;}i:50;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.747365;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9224888;}i:51;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.749027;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:147;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9228656;}i:53;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.750581;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9345664;}i:54;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.751765;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9347648;}i:56;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-981A70F0', 1, 'Поставка материалов', 2, '2025-06-01 17:17:06', 0, NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.752512;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9381176;}i:57;a:6:{i:0;s:271:"INSERT INTO "invoice" ("invoice_number", "supplier_id", "description", "source", "created_at", "total_amount", "accept_user_id", "deleted_at") VALUES ('20250601-981A70F0', 1, 'Поставка материалов', 2, '2025-06-01 17:17:06', 0, NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.75425;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:154;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:100;s:8:"function";s:13:"createInvoice";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9383816;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.755385;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9414504;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.758592;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9432416;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.759064;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9426544;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.761028;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:172;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9430896;}i:65;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=38)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.761645;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9454256;}i:66;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=38)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.762787;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9456272;}i:68;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763261;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9495400;}i:69;a:6:{i:0;s:64:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.764915;i:4;a:3:{i:0;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:179;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:89:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeService.php";s:4:"line";i:104;s:8:"function";s:19:"createInvoiceDetail";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:312;s:8:"function";s:20:"createMaterialIncome";s:5:"class";s:58:"app\modules\api\services\rawMaterial\MaterialIncomeService";s:4:"type";s:2:"->";}}i:5;i:9497416;}}}";s:5:"event";s:7740:"a:42:{i:0;a:5:{s:4:"time";d:**********.642473;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.651359;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.651387;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.67172;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.713497;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.733387;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.733437;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.733661;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.735371;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.73539;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.735396;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.7354;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.735404;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.735407;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.735411;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.735486;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.735529;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:17;a:5:{s:4:"time";d:**********.742719;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:18;a:5:{s:4:"time";d:**********.742773;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:44:"app\modules\api\models\IncomeRawMaterialForm";}i:19;a:5:{s:4:"time";d:**********.743268;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:20;a:5:{s:4:"time";d:**********.743707;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:21;a:5:{s:4:"time";d:**********.749199;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:22;a:5:{s:4:"time";d:**********.750184;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:**********.75021;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:**********.751995;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:25;a:5:{s:4:"time";d:**********.752008;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:26;a:5:{s:4:"time";d:**********.755185;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:27;a:5:{s:4:"time";d:**********.75532;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:28;a:5:{s:4:"time";d:**********.761417;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:29;a:5:{s:4:"time";d:**********.76155;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:30;a:5:{s:4:"time";d:**********.761572;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:31;a:5:{s:4:"time";d:**********.763167;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:**********.763186;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:33;a:5:{s:4:"time";d:**********.76532;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:34;a:5:{s:4:"time";d:**********.765681;s:4:"name";s:19:"rollbackTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:35;a:5:{s:4:"time";d:**********.766061;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:36;a:5:{s:4:"time";d:**********.766386;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:37;a:5:{s:4:"time";d:**********.766391;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:38;a:5:{s:4:"time";d:**********.766396;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:39;a:5:{s:4:"time";d:**********.766401;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:40;a:5:{s:4:"time";d:**********.767016;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:41;a:5:{s:4:"time";d:**********.767063;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.618029;s:3:"end";d:**********.768647;s:6:"memory";i:9633040;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2242:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642665;i:4;a:0:{}i:5;i:5773184;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642673;i:4;a:0:{}i:5;i:5773936;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642676;i:4;a:0:{}i:5;i:5774688;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642679;i:4;a:0:{}i:5;i:5775440;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642682;i:4;a:0:{}i:5;i:5776192;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642684;i:4;a:0:{}i:5;i:5776944;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642686;i:4;a:0:{}i:5;i:5777696;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642688;i:4;a:0:{}i:5;i:5778448;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642691;i:4;a:0:{}i:5;i:5779840;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.642699;i:4;a:0:{}i:5;i:5781936;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.642701;i:4;a:0:{}i:5;i:5781752;}}s:5:"route";s:36:"api/raw-material/accept-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";}";s:7:"request";s:4677:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"34fdf94f-39d7-483c-85fd-81d25dc48c11";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"199";s:6:"cookie";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=3988e2e6cb293c82782179df003784ca90780798fd221075cb1a068f654a4013a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22w0DSXrNR7jIq6EVTmtdPic4wgbmAdkik%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683c44c29c1f1";s:16:"X-Debug-Duration";s:3:"150";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683c44c29c1f1";s:10:"Set-Cookie";s:204:"_csrf=cc063d5c6ec66d68be8f46e3794796996172aef893564df29b543cdc3d183c28a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22ET2f0ODtW0M9Y3fUwqmMtSVvA2UpKCv5%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:36:"api/raw-material/accept-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionAcceptRawMaterial()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:199:"{
    "supplier_id": 1,
    "description": "Поставка материалов",
    "materials": [
        {
            "material_id": 10,
            "quantity": 20
        }
    ]
}
";s:7:"Decoded";a:3:{s:11:"supplier_id";i:1;s:11:"description";s:37:"Поставка материалов";s:9:"materials";a:1:{i:0;a:2:{s:11:"material_id";i:10;s:8:"quantity";i:20;}}}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"34fdf94f-39d7-483c-85fd-81d25dc48c11";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"199";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=3988e2e6cb293c82782179df003784ca90780798fd221075cb1a068f654a4013a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22w0DSXrNR7jIq6EVTmtdPic4wgbmAdkik%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"55953";s:12:"REDIRECT_URL";s:37:"/api/raw-material/accept-raw-material";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:37:"/api/raw-material/accept-raw-material";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.610817;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"ijocj4ons9v73rb97jao7352v8c6b4cn";s:5:"_csrf";s:130:"3988e2e6cb293c82782179df003784ca90780798fd221075cb1a068f654a4013a:2:{i:0;s:5:"_csrf";i:1;s:32:"w0DSXrNR7jIq6EVTmtdPic4wgbmAdkik";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2139:"a:5:{s:2:"id";i:6;s:8:"identity";a:8:{s:2:"id";s:1:"6";s:8:"username";s:12:"'raw_keeper'";s:9:"full_name";s:20:"'Hom ashyo ishchisi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";s:8:"password";s:62:"'$2y$13$bDj/q/7SKFwJIrGKBX7Z8O5TvDtnhjH0Q9YZKxl858G.7vVP8/a.m'";s:10:"created_at";s:21:"'2025-03-16 16:22:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:10:"raw_keeper";a:7:{s:4:"type";i:1;s:4:"name";s:10:"raw_keeper";s:11:"description";s:10:"Raw keeper";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683c44c29c1f1";s:3:"url";s:50:"http://silver/api/raw-material/accept-raw-material";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.610817;s:10:"statusCode";i:500;s:8:"sqlCount";i:13;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9633040;s:14:"processingTime";d:0.15041089057922363;}s:10:"exceptions";a:0:{}}