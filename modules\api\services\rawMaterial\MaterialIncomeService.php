<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Material;
use app\common\models\Tracking;
use app\common\models\ActionLogger;
use app\modules\api\models\IncomeRawMaterialForm;
use yii\base\Component;

/**
 * Сервис для работы с поступлением материалов
 */
class MaterialIncomeService extends Component
{
    /**
     * Получить список инвойсов за определенную дату
     * 
     * @param string|null $date Дата для фильтрации (по умолчанию текущая дата)
     * @return array Список инвойсов с материалами
     */
    public function getInvoicesList($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $query = Invoice::find()
            ->select([
                'invoice.id',
                'invoice.created_at',
                'supplier.full_name as supplier_name',
                new \yii\db\Expression('CASE
                    WHEN tracking.accepted_at IS NOT NULL
                    AND invoice.accepted_at IS NOT NULL
                    and invoice.accept_user_id is not null
                    and invoice.deleted_at is null
                    AND tracking.deleted_at IS NULL
                    THEN true
                    ELSE false
                END as status')
            ])
            ->join('LEFT JOIN', 'supplier', 'supplier.id = invoice.supplier_id')
            ->join('LEFT JOIN', 'tracking', 'tracking.process_id = invoice.id AND tracking.progress_type = ' . Tracking::TYPE_MATERIAL_INCOME)
            ->where(['and',
                ['invoice.deleted_at' => null],
                ['DATE(invoice.created_at)' => $date]
            ])
            ->orderBy(['invoice.created_at' => SORT_DESC]);

        $invoices = $query->asArray()->all();

        foreach ($invoices as &$invoice) {
            $details = InvoiceDetail::find()
                ->select([
                    'material.name as material_name',
                    'invoice_detail.quantity',
                    'invoice_detail.price as unit_price',
                    '(invoice_detail.price * invoice_detail.quantity) as total_price',
                    'material.unit_type',
                    new \yii\db\Expression('CASE
                        WHEN material.unit_type = 1 THEN \'Штука\'
                        WHEN material.unit_type = 2 THEN \'Килограмм\'
                        WHEN material.unit_type = 3 THEN \'Литр\'
                        ELSE \'Неизвестно\'
                    END as unit_type_name')
                ])
                ->join('LEFT JOIN', 'material', 'material.id = invoice_detail.material_id')
                ->where([
                    'invoice_detail.invoice_id' => $invoice['id'],
                    'invoice_detail.deleted_at' => null
                ])
                ->asArray()
                ->all();

            $invoice['materials'] = $details;
            $invoice['total_sum'] = array_sum(array_column($details, 'total_price'));
        }

        return $invoices;
    }

    /**
     * Создать новый инвойс с материалами
     * 
     * @param IncomeRawMaterialForm $form Валидированная форма с данными
     * @return array Результат операции
     */
    public function createMaterialIncome(IncomeRawMaterialForm $form)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Создаем новый инвойс
            $invoice = $this->createInvoice($form);
            
            // Создаем детали инвойса и обновляем склад
            foreach ($form->materials as $material) {
                $invoiceDetail = $this->createInvoiceDetail($invoice->id, $material);
                $this->updateMaterialStorage($material, $invoiceDetail->id);
            }
            
            // Создаем запись трекинга
            $this->createTracking($invoice->id);
            
            // Логируем действие
            $this->logAction($invoice, $form);
            
            $transaction->commit();
            
            return [
                'success' => true,
                'message' => 'Materials received successfully',
                'data' => [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number
                ]
            ];
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => 'Failed to accept materials',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Создать новый инвойс
     * 
     * @param IncomeRawMaterialForm $form
     * @return Invoice
     * @throws \Exception
     */
    private function createInvoice(IncomeRawMaterialForm $form)
    {
        $invoice = new Invoice();
        $prefix = date('Ymd');
        $random = bin2hex(random_bytes(4));
        $invoice->invoice_number = sprintf('%s-%s', $prefix, strtoupper($random));
        $invoice->supplier_id = (int)$form->supplier_id;
        $invoice->description = $form->description;
        $invoice->source = Invoice::SOURCE_MOBILE;
        $invoice->created_at = date('Y-m-d H:i:s');
        $invoice->total_amount = 0;

        if (!$invoice->save()) {
            throw new \Exception('Invoice save error: ' . json_encode($invoice->getErrors()));
        }

        return $invoice;
    }

    /**
     * Создать деталь инвойса
     * 
     * @param int $invoiceId
     * @param array $material
     * @return InvoiceDetail
     * @throws \Exception
     */
    private function createInvoiceDetail($invoiceId, $material)
    {
        $invoiceDetail = new InvoiceDetail();
        $invoiceDetail->invoice_id = $invoiceId;
        $invoiceDetail->material_id = $material['material_id'];
        $invoiceDetail->price = null;
        $invoiceDetail->quantity = $material['quantity'];
        $invoiceDetail->remainder_quantity = $material['quantity'];
        $invoiceDetail->created_at = date('Y-m-d H:i:s');

        if (!$invoiceDetail->save()) {
            throw new \Exception(json_encode($invoiceDetail->getErrors()));
        }

        return $invoiceDetail;
    }

    /**
     * Обновить склад материалов
     * 
     * @param array $material
     * @param int $invoiceDetailId
     * @throws \Exception
     */
    private function updateMaterialStorage($material, $invoiceDetailId)
    {
        // Ищем существующую запись склада за сегодня
        $storage = MaterialStorage::find()
            ->where(['material_id' => $material['material_id']])
            ->andWhere(['>=', 'updated_at', date('Y-m-d 00:00:00')])
            ->andWhere(['<=', 'updated_at', date('Y-m-d 23:59:59')])
            ->one();

        if (!$storage) {
            $storage = new MaterialStorage();
            $storage->material_id = $material['material_id'];
            $storage->created_at = date('Y-m-d H:i:s');
            $storage->quantity = 0;
        }

        $storage->quantity += $material['quantity'];
        $storage->updated_at = date('Y-m-d H:i:s');

        if (!$storage->save()) {
            throw new \Exception('Ошибка сохранения склада: ' . json_encode($storage->errors));
        }

        // Создаем запись истории
        $this->createStorageHistory($storage->id, $material, $invoiceDetailId);
    }

    /**
     * Создать запись истории склада
     * 
     * @param int $storageId
     * @param array $material
     * @param int $invoiceDetailId
     * @throws \Exception
     */
    private function createStorageHistory($storageId, $material, $invoiceDetailId)
    {
        $history = new MaterialStorageHistory();
        $history->material_storage_id = $storageId;
        $history->material_id = $material['material_id'];
        $history->quantity = $material['quantity'];
        $history->created_at = date('Y-m-d H:i:s');
        $history->add_user_id = Yii::$app->user->id;
        $history->type = MaterialStorageHistory::TYPE_INCOME;
        $history->invoice_detail_id = $invoiceDetailId;

        if (!$history->save()) {
            throw new \Exception('Ошибка сохранения истории: ' . json_encode($history->errors));
        }
    }

    /**
     * Создать запись трекинга
     * 
     * @param int $invoiceId
     * @throws \Exception
     */
    private function createTracking($invoiceId)
    {
        $tracking = new Tracking();
        $tracking->progress_type = Tracking::TYPE_MATERIAL_INCOME;
        $tracking->process_id = $invoiceId;
        $tracking->created_at = date('Y-m-d H:i:s');
        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
        $tracking->accepted_at = null;

        if (!$tracking->save()) {
            throw new \Exception('Tracking save error');
        }
    }

    /**
     * Логировать действие
     * 
     * @param Invoice $invoice
     * @param IncomeRawMaterialForm $form
     */
    private function logAction($invoice, $form)
    {
        ActionLogger::actionLog(
            'accept_raw_material',
            'invoice',
            null,
            [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'supplier_id' => $invoice->supplier_id,
                'total_amount' => $invoice->total_amount,
                'materials_count' => count($form->materials),
                'materials' => array_map(function($material) {
                    return [
                        'material_id' => $material['material_id'],
                        'quantity' => $material['quantity']
                    ];
                }, $form->materials)
            ]
        );
    }
}
